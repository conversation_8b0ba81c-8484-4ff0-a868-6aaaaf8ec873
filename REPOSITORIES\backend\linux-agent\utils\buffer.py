"""
Buffer utilities for Linux Log Collection Agent

This module provides buffering functionality for log entries before they are
sent to the API or written to output files.
"""

import logging
import threading
import time
from collections import deque
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable


class TimedBuffer:
    """
    A thread-safe buffer that holds log entries and flushes them based on
    size or time thresholds.
    """
    
    def __init__(self, 
                 max_size: int = 1000,
                 max_age_seconds: int = 30,
                 flush_callback: Optional[Callable[[List[Dict[str, Any]]], None]] = None):
        """
        Initialize the timed buffer.
        
        Args:
            max_size: Maximum number of entries before auto-flush
            max_age_seconds: Maximum age of oldest entry before auto-flush
            flush_callback: Function to call when flushing entries
        """
        self.max_size = max_size
        self.max_age_seconds = max_age_seconds
        self.flush_callback = flush_callback
        
        self._buffer = deque()
        self._lock = threading.RLock()
        self._running = False
        self._flush_thread = None
        
        self.logger = logging.getLogger(__name__)
        
        # Statistics
        self.stats = {
            'entries_added': 0,
            'entries_flushed': 0,
            'flushes_by_size': 0,
            'flushes_by_time': 0,
            'flushes_manual': 0,
            'last_flush': None
        }
    
    def start(self) -> None:
        """Start the buffer's background flush thread."""
        if self._running:
            return
        
        self._running = True
        self._flush_thread = threading.Thread(target=self._flush_worker, daemon=True)
        self._flush_thread.start()
        
        self.logger.debug("TimedBuffer started")
    
    def stop(self) -> None:
        """Stop the buffer and flush remaining entries."""
        if not self._running:
            return
        
        self._running = False
        
        # Wait for flush thread to finish
        if self._flush_thread and self._flush_thread.is_alive():
            self._flush_thread.join(timeout=5)
        
        # Flush remaining entries
        self.flush()
        
        self.logger.debug("TimedBuffer stopped")
    
    def add(self, entry: Dict[str, Any]) -> None:
        """
        Add an entry to the buffer.
        
        Args:
            entry: Log entry to add
        """
        with self._lock:
            # Add timestamp if not present
            if 'buffer_timestamp' not in entry:
                entry['buffer_timestamp'] = datetime.now()
            
            self._buffer.append(entry)
            self.stats['entries_added'] += 1
            
            # Check if we need to flush due to size
            if len(self._buffer) >= self.max_size:
                self._flush_internal(reason='size')
    
    def add_many(self, entries: List[Dict[str, Any]]) -> None:
        """
        Add multiple entries to the buffer.
        
        Args:
            entries: List of log entries to add
        """
        if not entries:
            return
        
        with self._lock:
            current_time = datetime.now()
            
            for entry in entries:
                if 'buffer_timestamp' not in entry:
                    entry['buffer_timestamp'] = current_time
                self._buffer.append(entry)
            
            self.stats['entries_added'] += len(entries)
            
            # Check if we need to flush due to size
            if len(self._buffer) >= self.max_size:
                self._flush_internal(reason='size')
    
    def flush(self) -> int:
        """
        Manually flush all entries in the buffer.
        
        Returns:
            Number of entries flushed
        """
        with self._lock:
            return self._flush_internal(reason='manual')
    
    def _flush_internal(self, reason: str = 'unknown') -> int:
        """
        Internal flush method (must be called with lock held).
        
        Args:
            reason: Reason for flushing (for statistics)
            
        Returns:
            Number of entries flushed
        """
        if not self._buffer:
            return 0
        
        # Get all entries
        entries = []
        while self._buffer:
            entry = self._buffer.popleft()
            # Remove buffer timestamp before flushing
            if 'buffer_timestamp' in entry:
                del entry['buffer_timestamp']
            entries.append(entry)
        
        # Update statistics
        count = len(entries)
        self.stats['entries_flushed'] += count
        self.stats['last_flush'] = datetime.now()
        
        if reason == 'size':
            self.stats['flushes_by_size'] += 1
        elif reason == 'time':
            self.stats['flushes_by_time'] += 1
        elif reason == 'manual':
            self.stats['flushes_manual'] += 1
        
        # Call flush callback if provided
        if self.flush_callback and entries:
            try:
                self.flush_callback(entries)
                self.logger.debug(f"Flushed {count} entries (reason: {reason})")
            except Exception as e:
                self.logger.error(f"Error in flush callback: {e}")
                # Re-add entries to buffer if callback failed
                with self._lock:
                    for entry in reversed(entries):
                        entry['buffer_timestamp'] = datetime.now()
                        self._buffer.appendleft(entry)
                return 0
        
        return count
    
    def _flush_worker(self) -> None:
        """Background worker that flushes entries based on age."""
        while self._running:
            try:
                time.sleep(1)  # Check every second
                
                with self._lock:
                    if not self._buffer:
                        continue
                    
                    # Check age of oldest entry
                    oldest_entry = self._buffer[0]
                    oldest_time = oldest_entry.get('buffer_timestamp')
                    
                    if oldest_time:
                        age = datetime.now() - oldest_time
                        if age.total_seconds() >= self.max_age_seconds:
                            self._flush_internal(reason='time')
                
            except Exception as e:
                self.logger.error(f"Error in flush worker: {e}")
                time.sleep(5)  # Wait before retrying
    
    def size(self) -> int:
        """Get current buffer size."""
        with self._lock:
            return len(self._buffer)
    
    def is_empty(self) -> bool:
        """Check if buffer is empty."""
        with self._lock:
            return len(self._buffer) == 0
    
    def clear(self) -> int:
        """
        Clear all entries from the buffer without flushing.
        
        Returns:
            Number of entries cleared
        """
        with self._lock:
            count = len(self._buffer)
            self._buffer.clear()
            return count
    
    def get_stats(self) -> Dict[str, Any]:
        """Get buffer statistics."""
        with self._lock:
            stats = self.stats.copy()
            stats['current_size'] = len(self._buffer)
            stats['running'] = self._running
            
            # Calculate oldest entry age
            if self._buffer:
                oldest_entry = self._buffer[0]
                oldest_time = oldest_entry.get('buffer_timestamp')
                if oldest_time:
                    stats['oldest_entry_age_seconds'] = (datetime.now() - oldest_time).total_seconds()
            
            return stats


class CircularBuffer:
    """
    A circular buffer that maintains a fixed number of recent log entries.
    Useful for keeping recent logs in memory for debugging or status display.
    """
    
    def __init__(self, max_size: int = 100):
        """
        Initialize the circular buffer.
        
        Args:
            max_size: Maximum number of entries to keep
        """
        self.max_size = max_size
        self._buffer = deque(maxlen=max_size)
        self._lock = threading.RLock()
        
        self.stats = {
            'entries_added': 0,
            'entries_dropped': 0
        }
    
    def add(self, entry: Dict[str, Any]) -> None:
        """
        Add an entry to the circular buffer.
        
        Args:
            entry: Log entry to add
        """
        with self._lock:
            # Check if we're about to drop an entry
            if len(self._buffer) == self.max_size:
                self.stats['entries_dropped'] += 1
            
            # Add timestamp
            entry_with_timestamp = entry.copy()
            entry_with_timestamp['buffer_timestamp'] = datetime.now()
            
            self._buffer.append(entry_with_timestamp)
            self.stats['entries_added'] += 1
    
    def get_recent(self, count: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Get recent entries from the buffer.
        
        Args:
            count: Number of entries to return (None for all)
            
        Returns:
            List of recent log entries
        """
        with self._lock:
            if count is None:
                return list(self._buffer)
            else:
                return list(self._buffer)[-count:]
    
    def clear(self) -> None:
        """Clear all entries from the buffer."""
        with self._lock:
            self._buffer.clear()
    
    def size(self) -> int:
        """Get current buffer size."""
        with self._lock:
            return len(self._buffer)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get buffer statistics."""
        with self._lock:
            stats = self.stats.copy()
            stats['current_size'] = len(self._buffer)
            stats['max_size'] = self.max_size
            return stats
