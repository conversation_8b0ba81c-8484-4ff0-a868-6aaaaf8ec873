"""
Kernel Log Collector for Linux Log Collection Agent

This module collects kernel logs from /var/log/kern.log and other
kernel-related log files on Linux systems.
"""

import logging
import os
import re
from datetime import datetime
from typing import Dict, List, Any, Optional

from .base_collector import BaseLogCollector


class KernelLogCollector(BaseLogCollector):
    """Collector for Linux kernel logs."""
    
    def __init__(self, config: Dict[str, Any], logger: Optional[logging.Logger] = None):
        """
        Initialize the kernel log collector.
        
        Args:
            config: Collector configuration
            logger: Logger instance
        """
        super().__init__(config, logger)
        
        # Kernel-specific configuration
        self.real_time = config.get('real_time', True)
        
        # Default kernel log paths if none specified
        if not config.get('paths'):
            self.config['paths'] = self._get_default_kernel_paths()
        
        self.logger.info(f"Kernel log collector initialized with {len(self.config['paths'])} paths")
    
    def _get_default_kernel_paths(self) -> List[str]:
        """Get default kernel log paths for different distributions."""
        default_paths = [
            '/var/log/kern.log',    # Ubuntu, Debian
            '/var/log/kernel',      # Some distributions
            '/var/log/dmesg'        # Boot messages
        ]
        
        existing_paths = []
        for path in default_paths:
            if os.path.exists(path) and os.access(path, os.R_OK):
                existing_paths.append(path)
        
        return existing_paths
    
    def collect_logs(self) -> List[Dict[str, Any]]:
        """
        Collect logs from kernel log files.
        
        Returns:
            List of collected log entries
        """
        collected_logs = []
        
        try:
            log_paths = self._get_log_paths()
            
            if not log_paths:
                self.logger.warning("No accessible kernel log files found")
                return collected_logs
            
            for log_path in log_paths:
                try:
                    # Read new lines from the file
                    new_lines = self._read_file_from_position(log_path)
                    
                    if new_lines:
                        self.logger.debug(f"Processing {len(new_lines)} new kernel log lines from {log_path}")
                        
                        for line in new_lines:
                            log_entry = self._parse_kernel_log_line(line)
                            if log_entry:
                                # Add file-specific metadata
                                log_entry['additional_fields']['log_file'] = log_path
                                log_entry['additional_fields']['collector'] = 'kernel'
                                
                                # Sanitize message
                                log_entry['message'] = self._sanitize_message(log_entry['message'])
                                
                                collected_logs.append(log_entry)
                        
                        self.stats['files_processed'] += 1
                
                except Exception as e:
                    self.logger.error(f"Error processing kernel log file {log_path}: {e}")
                    self.stats['errors'] += 1
            
            if collected_logs:
                self.stats['logs_collected'] += len(collected_logs)
                self.stats['last_collection'] = datetime.now()
                self.logger.debug(f"Collected {len(collected_logs)} kernel log entries")
            
        except Exception as e:
            self.logger.error(f"Error in kernel log collection: {e}")
            self.stats['errors'] += 1
        
        return collected_logs
    
    def _parse_kernel_log_line(self, line: str) -> Optional[Dict[str, Any]]:
        """
        Parse a kernel log line with enhanced kernel-specific parsing.
        
        Args:
            line: Log line to parse
            
        Returns:
            Parsed log entry or None if parsing failed
        """
        # Use base class parsing first
        log_entry = self._parse_syslog_line(line, source="Kernel")
        
        if not log_entry:
            return None
        
        # Set source type to kernel
        log_entry['source_type'] = 'kernel'
        
        # Enhanced kernel-specific processing
        try:
            message = log_entry['message']
            additional_fields = log_entry['additional_fields']
            
            # Extract kernel timestamp if present (e.g., [12345.678901])
            kernel_time = self._extract_kernel_timestamp(message)
            if kernel_time:
                additional_fields['kernel_timestamp'] = kernel_time
            
            # Classify kernel event
            kernel_event_type = self._classify_kernel_event(message)
            if kernel_event_type:
                additional_fields['kernel_event_type'] = kernel_event_type
                
                # Adjust log level based on event type
                if kernel_event_type in ['oops', 'panic', 'bug', 'segfault']:
                    log_entry['log_level'] = 'critical'
                elif kernel_event_type in ['error', 'warning']:
                    log_entry['log_level'] = 'error' if kernel_event_type == 'error' else 'warning'
            
            # Extract hardware information
            hardware_info = self._extract_hardware_info(message)
            if hardware_info:
                additional_fields['hardware'] = hardware_info
            
            # Extract driver information
            driver_info = self._extract_driver_info(message)
            if driver_info:
                additional_fields['driver'] = driver_info
            
            # Extract module information
            module_info = self._extract_module_info(message)
            if module_info:
                additional_fields['module'] = module_info
            
            # Extract memory information
            memory_info = self._extract_memory_info(message)
            if memory_info:
                additional_fields['memory'] = memory_info
            
        except Exception as e:
            self.logger.error(f"Error in enhanced kernel log parsing: {e}")
        
        return log_entry
    
    def _extract_kernel_timestamp(self, message: str) -> Optional[float]:
        """Extract kernel timestamp from message."""
        # Look for kernel timestamp format [12345.678901]
        timestamp_pattern = r'\[(\d+\.\d+)\]'
        match = re.search(timestamp_pattern, message)
        if match:
            return float(match.group(1))
        return None
    
    def _classify_kernel_event(self, message: str) -> Optional[str]:
        """Classify the type of kernel event."""
        message_lower = message.lower()
        
        # Critical kernel events
        if any(critical in message_lower for critical in ['panic', 'oops', 'bug', 'segfault']):
            if 'panic' in message_lower:
                return 'panic'
            elif 'oops' in message_lower:
                return 'oops'
            elif 'bug' in message_lower:
                return 'bug'
            elif 'segfault' in message_lower:
                return 'segfault'
        
        # Hardware events
        if any(hw in message_lower for hw in ['usb', 'pci', 'acpi', 'cpu', 'memory', 'disk']):
            return 'hardware'
        
        # Driver events
        if 'driver' in message_lower or any(drv in message_lower for drv in ['loaded', 'unloaded', 'probe']):
            return 'driver'
        
        # Network events
        if any(net in message_lower for net in ['network', 'eth', 'wlan', 'interface']):
            return 'network'
        
        # File system events
        if any(fs in message_lower for fs in ['ext4', 'xfs', 'btrfs', 'mount', 'filesystem']):
            return 'filesystem'
        
        # Module events
        if 'module' in message_lower:
            return 'module'
        
        # Memory events
        if any(mem in message_lower for mem in ['memory', 'oom', 'swap']):
            return 'memory'
        
        # Error/warning classification
        if any(err in message_lower for err in ['error', 'failed', 'failure']):
            return 'error'
        elif any(warn in message_lower for warn in ['warning', 'warn']):
            return 'warning'
        
        return None
    
    def _extract_hardware_info(self, message: str) -> Optional[Dict[str, Any]]:
        """Extract hardware-related information from message."""
        hardware_info = {}
        message_lower = message.lower()
        
        # USB devices
        if 'usb' in message_lower:
            hardware_info['type'] = 'usb'
            
            # Extract USB device ID
            usb_id_pattern = r'(\d{4}:\d{4})'
            usb_match = re.search(usb_id_pattern, message)
            if usb_match:
                hardware_info['device_id'] = usb_match.group(1)
        
        # PCI devices
        elif 'pci' in message_lower:
            hardware_info['type'] = 'pci'
            
            # Extract PCI address
            pci_pattern = r'(\d{4}:\d{2}:\d{2}\.\d)'
            pci_match = re.search(pci_pattern, message)
            if pci_match:
                hardware_info['pci_address'] = pci_match.group(1)
        
        # CPU information
        elif 'cpu' in message_lower:
            hardware_info['type'] = 'cpu'
            
            # Extract CPU number
            cpu_pattern = r'cpu\s*(\d+)'
            cpu_match = re.search(cpu_pattern, message, re.IGNORECASE)
            if cpu_match:
                hardware_info['cpu_number'] = int(cpu_match.group(1))
        
        # Disk/storage
        elif any(disk in message_lower for disk in ['disk', 'sda', 'sdb', 'nvme']):
            hardware_info['type'] = 'storage'
            
            # Extract device name
            device_pattern = r'(sd[a-z]|nvme\d+n\d+)'
            device_match = re.search(device_pattern, message)
            if device_match:
                hardware_info['device'] = device_match.group(1)
        
        return hardware_info if hardware_info else None
    
    def _extract_driver_info(self, message: str) -> Optional[Dict[str, Any]]:
        """Extract driver-related information from message."""
        driver_info = {}
        
        # Extract driver name
        driver_patterns = [
            r'driver\s+(\w+)',
            r'(\w+)\s+driver',
            r'(\w+):\s+probe'
        ]
        
        for pattern in driver_patterns:
            match = re.search(pattern, message, re.IGNORECASE)
            if match:
                driver_info['name'] = match.group(1)
                break
        
        # Driver actions
        if 'loaded' in message.lower():
            driver_info['action'] = 'loaded'
        elif 'unloaded' in message.lower():
            driver_info['action'] = 'unloaded'
        elif 'probe' in message.lower():
            driver_info['action'] = 'probe'
        elif 'failed' in message.lower():
            driver_info['action'] = 'failed'
        
        return driver_info if driver_info else None
    
    def _extract_module_info(self, message: str) -> Optional[Dict[str, Any]]:
        """Extract kernel module information from message."""
        module_info = {}
        
        # Extract module name
        module_pattern = r'module\s+(\w+)'
        module_match = re.search(module_pattern, message, re.IGNORECASE)
        if module_match:
            module_info['name'] = module_match.group(1)
        
        # Module actions
        if 'loaded' in message.lower():
            module_info['action'] = 'loaded'
        elif 'unloaded' in message.lower():
            module_info['action'] = 'unloaded'
        elif 'tainted' in message.lower():
            module_info['tainted'] = True
        
        return module_info if module_info else None
    
    def _extract_memory_info(self, message: str) -> Optional[Dict[str, Any]]:
        """Extract memory-related information from message."""
        memory_info = {}
        message_lower = message.lower()
        
        # Out of memory events
        if 'oom' in message_lower:
            memory_info['type'] = 'oom'
            
            # Extract killed process
            killed_pattern = r'killed process (\d+) \(([^)]+)\)'
            killed_match = re.search(killed_pattern, message)
            if killed_match:
                memory_info['killed_pid'] = int(killed_match.group(1))
                memory_info['killed_process'] = killed_match.group(2)
        
        # Memory allocation failures
        elif 'allocation' in message_lower and 'failed' in message_lower:
            memory_info['type'] = 'allocation_failure'
        
        # Swap events
        elif 'swap' in message_lower:
            memory_info['type'] = 'swap'
        
        return memory_info if memory_info else None
    
    def start(self) -> None:
        """Start the kernel log collector."""
        super().start()
        
        # Log accessible kernel log files
        log_paths = self._get_log_paths()
        if log_paths:
            self.logger.info(f"Monitoring kernel log files: {log_paths}")
        else:
            self.logger.warning("No accessible kernel log files found")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get kernel log collector statistics."""
        stats = super().get_stats()
        stats['collector_type'] = 'kernel'
        stats['monitored_files'] = self._get_log_paths()
        return stats
