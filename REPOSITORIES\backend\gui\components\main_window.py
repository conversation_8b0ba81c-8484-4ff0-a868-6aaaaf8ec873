"""
Main window component for the Logging Agent GUI
"""

import flet as ft
import asyncio
from typing import Optional
import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from gui.components.sidebar import Sidebar
from gui.components.dashboard import Dashboard
from gui.components.configuration import Configuration
from gui.components.service_control import ServiceControl
from gui.components.logs_viewer import LogsViewer
from gui.components.about import About
from gui.utils.theme import AppTheme

class MainWindow(ft.Control):
    """Main application window"""
    
    def __init__(self, page: ft.Page, logger):
        super().__init__()
        self.page = page
        self.logger = logger
        self.current_view = "dashboard"
        
        # Components
        self.sidebar: Optional[Sidebar] = None
        self.content_area: Optional[ft.Container] = None
        self.views = {}
        
    async def initialize(self):
        """Initialize the main window"""
        try:
            self.logger.info("Initializing main window")
            
            # Create sidebar
            self.sidebar = Sidebar(self.on_navigation_change, self.logger)
            await self.sidebar.initialize()
            
            # Create views
            await self._create_views()
            
            # Create content area
            self.content_area = ft.Container(
                content=self.views[self.current_view],
                expand=True,
                padding=0,
                bgcolor=AppTheme.BACKGROUND_COLOR
            )
            
            self.logger.info("Main window initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Error initializing main window: {e}")
            raise
    
    async def _create_views(self):
        """Create all view components"""
        try:
            # Dashboard
            dashboard = Dashboard(self.page, self.logger)
            await dashboard.initialize()
            self.views["dashboard"] = dashboard
            
            # Configuration
            configuration = Configuration(self.page, self.logger)
            await configuration.initialize()
            self.views["configuration"] = configuration
            
            # Service Control
            service_control = ServiceControl(self.page, self.logger)
            await service_control.initialize()
            self.views["service"] = service_control
            
            # Logs Viewer
            logs_viewer = LogsViewer(self.page, self.logger)
            await logs_viewer.initialize()
            self.views["logs"] = logs_viewer
            
            # About
            about = About(self.page, self.logger)
            await about.initialize()
            self.views["about"] = about
            
        except Exception as e:
            self.logger.error(f"Error creating views: {e}")
            raise
    
    async def on_navigation_change(self, view_name: str):
        """Handle navigation changes"""
        try:
            self.logger.info(f"Navigating to view: {view_name}")
            
            if view_name in self.views:
                self.current_view = view_name
                self.content_area.content = self.views[view_name]
                
                # Refresh the view if it has a refresh method
                if hasattr(self.views[view_name], 'refresh'):
                    await self.views[view_name].refresh()
                
                await self.content_area.update_async()
                
        except Exception as e:
            self.logger.error(f"Error changing navigation: {e}")
    
    def build(self):
        """Build the main window layout"""
        return ft.Row(
            controls=[
                # Sidebar
                ft.Container(
                    content=self.sidebar,
                    width=250,
                    bgcolor=AppTheme.SURFACE_COLOR,
                    border=ft.border.only(right=ft.BorderSide(1, AppTheme.OUTLINE_COLOR)),
                    padding=0
                ),
                # Content area
                self.content_area
            ],
            spacing=0,
            expand=True
        )
