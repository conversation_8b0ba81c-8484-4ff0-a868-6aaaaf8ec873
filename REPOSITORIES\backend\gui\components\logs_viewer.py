"""
Logs viewer component for displaying application logs
"""

import flet as ft
import asyncio
from pathlib import Path
from datetime import datetime
from typing import List, Optional
import sys

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from gui.utils.theme import AppTheme
from gui.utils.logger import GUILogHandler

class LogsViewer(ft.Control):
    """Logs viewer component"""
    
    def __init__(self, page: ft.Page, logger):
        super().__init__()
        self.page = page
        self.logger = logger
        
        # Controls
        self.logs_list: Optional[ft.ListView] = None
        self.level_filter: Optional[ft.Dropdown] = None
        self.auto_refresh_switch: Optional[ft.Switch] = None
        self.clear_button: Optional[ft.ElevatedButton] = None
        self.refresh_button: Optional[ft.ElevatedButton] = None
        self.export_button: Optional[ft.ElevatedButton] = None
        
        # State
        self.log_entries: List[dict] = []
        self.auto_refresh = True
        self.current_filter = "ALL"
        
        # Log handler
        self.log_handler: Optional[GUILogHandler] = None
        
    async def initialize(self):
        """Initialize the logs viewer component"""
        try:
            self.logger.info("Initializing logs viewer component")
            
            # Create controls
            self._create_controls()
            
            # Setup log handler
            self._setup_log_handler()
            
            # Load initial logs
            await self._load_logs()
            
            # Start auto-refresh if enabled
            if self.auto_refresh:
                asyncio.create_task(self._auto_refresh_loop())
            
            self.logger.info("Logs viewer component initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Error initializing logs viewer component: {e}")
            raise
    
    def _create_controls(self):
        """Create UI controls"""
        # Logs list
        self.logs_list = ft.ListView(
            expand=True,
            spacing=4,
            padding=ft.padding.all(8)
        )
        
        # Level filter
        self.level_filter = ft.Dropdown(
            label="Filter by Level",
            value="ALL",
            width=150,
            options=[
                ft.dropdown.Option("ALL"),
                ft.dropdown.Option("DEBUG"),
                ft.dropdown.Option("INFO"),
                ft.dropdown.Option("WARNING"),
                ft.dropdown.Option("ERROR"),
                ft.dropdown.Option("CRITICAL")
            ],
            on_change=self._on_filter_change
        )
        
        # Auto-refresh switch
        self.auto_refresh_switch = ft.Switch(
            label="Auto Refresh",
            value=self.auto_refresh,
            on_change=self._on_auto_refresh_change
        )
        
        # Action buttons
        self.clear_button = ft.ElevatedButton(
            "Clear Logs",
            icon=ft.icons.CLEAR,
            on_click=self._clear_logs,
            **AppTheme.get_button_style("outline")
        )
        
        self.refresh_button = ft.ElevatedButton(
            "Refresh",
            icon=ft.icons.REFRESH,
            on_click=self._refresh_logs,
            **AppTheme.get_button_style("secondary")
        )
        
        self.export_button = ft.ElevatedButton(
            "Export",
            icon=ft.icons.DOWNLOAD,
            on_click=self._export_logs,
            **AppTheme.get_button_style("outline")
        )
    
    def _setup_log_handler(self):
        """Setup custom log handler to capture logs"""
        self.log_handler = GUILogHandler(callback=self._on_new_log)
        self.log_handler.setLevel(10)  # Capture all levels
        
        # Add to root logger to capture all logs
        import logging
        root_logger = logging.getLogger()
        root_logger.addHandler(self.log_handler)
    
    async def _on_new_log(self, log_entry: dict):
        """Handle new log entry"""
        if self.auto_refresh:
            await self._add_log_entry(log_entry)
    
    async def _load_logs(self):
        """Load logs from files"""
        try:
            # Load from log handler if available
            if self.log_handler:
                logs = self.log_handler.get_logs(limit=100)
                for log in logs:
                    await self._add_log_entry(log)
            
            # Also try to load from log files
            await self._load_from_files()
            
        except Exception as e:
            self.logger.error(f"Error loading logs: {e}")
    
    async def _load_from_files(self):
        """Load logs from log files"""
        try:
            log_dir = Path("logs")
            if not log_dir.exists():
                return
            
            # Load recent log files
            log_files = list(log_dir.glob("*.log"))
            log_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            
            # Read the most recent log file
            if log_files:
                recent_file = log_files[0]
                with open(recent_file, 'r', encoding='utf-8', errors='ignore') as f:
                    lines = f.readlines()[-50:]  # Last 50 lines
                
                for line in lines:
                    if line.strip():
                        # Parse log line (basic parsing)
                        parts = line.strip().split(' - ', 3)
                        if len(parts) >= 3:
                            log_entry = {
                                'timestamp': datetime.now(),
                                'level': parts[2] if len(parts) > 2 else 'INFO',
                                'message': parts[-1] if len(parts) > 3 else line.strip(),
                                'module': 'file',
                                'function': '',
                                'line': 0
                            }
                            await self._add_log_entry(log_entry)
            
        except Exception as e:
            self.logger.error(f"Error loading from files: {e}")
    
    async def _add_log_entry(self, log_entry: dict):
        """Add a log entry to the display"""
        try:
            # Apply filter
            if self.current_filter != "ALL" and log_entry['level'] != self.current_filter:
                return
            
            # Create log item
            log_item = self._create_log_item(log_entry)
            
            # Add to list (keep only recent entries)
            self.logs_list.controls.insert(0, log_item)
            if len(self.logs_list.controls) > 200:
                self.logs_list.controls = self.logs_list.controls[:200]
            
            # Update UI if auto-refresh is enabled
            if self.auto_refresh:
                await self.logs_list.update_async()
            
        except Exception as e:
            self.logger.error(f"Error adding log entry: {e}")
    
    def _create_log_item(self, log_entry: dict) -> ft.Container:
        """Create a log item widget"""
        level = log_entry['level']
        level_color = self._get_level_color(level)
        
        timestamp_str = log_entry['timestamp'].strftime('%H:%M:%S')
        
        return ft.Container(
            content=ft.Row(
                controls=[
                    ft.Container(
                        content=ft.Text(
                            level,
                            size=10,
                            weight=ft.FontWeight.BOLD,
                            color=ft.colors.WHITE
                        ),
                        bgcolor=level_color,
                        padding=ft.padding.symmetric(horizontal=6, vertical=2),
                        border_radius=4,
                        width=60
                    ),
                    ft.Text(
                        timestamp_str,
                        size=11,
                        color=AppTheme.ON_BACKGROUND,
                        width=60
                    ),
                    ft.Text(
                        log_entry['message'],
                        size=11,
                        color=AppTheme.ON_SURFACE,
                        expand=True,
                        overflow=ft.TextOverflow.ELLIPSIS
                    )
                ],
                spacing=8,
                alignment=ft.MainAxisAlignment.START
            ),
            padding=ft.padding.symmetric(horizontal=8, vertical=4),
            border_radius=4,
            bgcolor=ft.colors.with_opacity(0.02, AppTheme.ON_SURFACE),
            border=ft.border.all(1, ft.colors.with_opacity(0.1, AppTheme.OUTLINE_COLOR))
        )
    
    def _get_level_color(self, level: str) -> str:
        """Get color for log level"""
        colors = {
            'DEBUG': '#6b7280',
            'INFO': AppTheme.INFO_COLOR,
            'WARNING': AppTheme.WARNING_COLOR,
            'ERROR': AppTheme.ERROR_COLOR,
            'CRITICAL': '#dc2626'
        }
        return colors.get(level, AppTheme.INFO_COLOR)
    
    async def _on_filter_change(self, e):
        """Handle filter change"""
        self.current_filter = e.control.value
        await self._refresh_logs()
    
    async def _on_auto_refresh_change(self, e):
        """Handle auto-refresh toggle"""
        self.auto_refresh = e.control.value
        if self.auto_refresh:
            asyncio.create_task(self._auto_refresh_loop())
    
    async def _auto_refresh_loop(self):
        """Auto-refresh loop"""
        while self.auto_refresh:
            try:
                await asyncio.sleep(2)  # Refresh every 2 seconds
                if self.auto_refresh:  # Check again in case it was disabled
                    await self.logs_list.update_async()
            except Exception as e:
                self.logger.error(f"Error in auto-refresh loop: {e}")
                break
    
    async def _clear_logs(self, e):
        """Clear all logs"""
        try:
            self.logs_list.controls.clear()
            if self.log_handler:
                self.log_handler.clear_logs()
            await self.logs_list.update_async()
        except Exception as e:
            self.logger.error(f"Error clearing logs: {e}")
    
    async def _refresh_logs(self, e=None):
        """Refresh logs display"""
        try:
            self.logs_list.controls.clear()
            await self._load_logs()
            await self.logs_list.update_async()
        except Exception as e:
            self.logger.error(f"Error refreshing logs: {e}")
    
    async def _export_logs(self, e):
        """Export logs to file"""
        try:
            # TODO: Implement log export functionality
            pass
        except Exception as e:
            self.logger.error(f"Error exporting logs: {e}")
    
    def build(self):
        """Build the logs viewer interface"""
        return ft.Container(
            content=ft.Column(
                controls=[
                    # Header
                    ft.Container(
                        content=ft.Row(
                            controls=[
                                ft.Text(
                                    "Application Logs",
                                    size=24,
                                    weight=ft.FontWeight.BOLD,
                                    color=AppTheme.ON_SURFACE
                                ),
                                ft.Row(
                                    controls=[
                                        self.level_filter,
                                        self.auto_refresh_switch,
                                        self.clear_button,
                                        self.refresh_button,
                                        self.export_button
                                    ],
                                    spacing=12
                                )
                            ],
                            alignment=ft.MainAxisAlignment.SPACE_BETWEEN
                        ),
                        padding=20
                    ),
                    
                    # Logs display
                    ft.Container(
                        content=self.logs_list,
                        **AppTheme.get_card_style(),
                        expand=True,
                        margin=ft.margin.symmetric(horizontal=20, vertical=0)
                    )
                ],
                spacing=0,
                expand=True
            ),
            expand=True,
            bgcolor=AppTheme.BACKGROUND_COLOR
        )
