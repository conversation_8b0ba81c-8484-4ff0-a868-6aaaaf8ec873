{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mWebSocket server started on port 5001\u001b[39m","timestamp":"2025-07-16 01:16:13:1613"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mReceived SIGTERM, shutting down WebSocket server...\u001b[39m","timestamp":"2025-07-16 01:17:44:1744"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mWebSocket server started on port 5001\u001b[39m","timestamp":"2025-07-16 01:22:50:2250"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mMongoDB connected successfully\u001b[39m","timestamp":"2025-07-16 01:22:54:2254"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connected successfully\u001b[39m","timestamp":"2025-07-16 01:22:54:2254"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll database connections established\u001b[39m","timestamp":"2025-07-16 01:22:54:2254"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAPI documentation enabled at /api/docs\u001b[39m","timestamp":"2025-07-16 01:22:54:2254"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mInitializing alert system...\u001b[39m","timestamp":"2025-07-16 01:22:54:2254"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mInitializing Correlation Engine...\u001b[39m","timestamp":"2025-07-16 01:22:54:2254"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mLoaded 8 alert rules into correlation engine\u001b[39m","timestamp":"2025-07-16 01:22:55:2255"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mCorrelation Engine initialized with 8 rules\u001b[39m","timestamp":"2025-07-16 01:22:55:2255"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mFound 8 existing default rules\u001b[39m","timestamp":"2025-07-16 01:22:55:2255"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAlert system initialization completed\u001b[39m","timestamp":"2025-07-16 01:22:55:2255"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mStarting agent tracking service\u001b[39m","timestamp":"2025-07-16 01:22:55:2255"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mExLog API server running on port 5000\u001b[39m","timestamp":"2025-07-16 01:22:55:2255"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: development\u001b[39m","timestamp":"2025-07-16 01:22:55:2255"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mServer accessible on all network interfaces\u001b[39m","timestamp":"2025-07-16 01:22:55:2255"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in successfully: <EMAIL> from IP: **********\u001b[39m","timestamp":"2025-07-16 01:23:11:2311"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mEmail service not initialized: missing configuration\u001b[39m","timestamp":"2025-07-16 01:23:11:2311"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to send login notification <NAME_EMAIL>: Email service is not properly configured\u001b[39m","stack":"Error: Email service is not properly configured\n    at EmailService.sendEmail (/app/src/services/emailService.js:93:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async EmailService.sendTemplatedEmail (/app/src/services/emailService.js:335:12)\n    at async EmailService.sendLoginNotificationEmail (/app/src/services/emailService.js:361:12)\n    at async Immediate.<anonymous> (/app/src/routes/auth.js:355:9)","timestamp":"2025-07-16 01:23:11:2311"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in successfully: <EMAIL> from IP: ************\u001b[39m","timestamp":"2025-07-16 01:24:27:2427"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mEmail service not initialized: missing configuration\u001b[39m","timestamp":"2025-07-16 01:24:27:2427"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to send login notification <NAME_EMAIL>: Email service is not properly configured\u001b[39m","stack":"Error: Email service is not properly configured\n    at EmailService.sendEmail (/app/src/services/emailService.js:93:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async EmailService.sendTemplatedEmail (/app/src/services/emailService.js:335:12)\n    at async EmailService.sendLoginNotificationEmail (/app/src/services/emailService.js:361:12)\n    at async Immediate.<anonymous> (/app/src/routes/auth.js:355:9)","timestamp":"2025-07-16 01:24:27:2427"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAuto-registered new agent: 6855a02042f295c34575d31d_web-server-01 (web-server-01)\u001b[39m","timestamp":"2025-07-16 01:24:35:2435"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessing log test-1752629075848-001 through correlation engine\u001b[39m","timestamp":"2025-07-16 01:24:36:2436"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mCorrelation engine processing completed for log test-1752629075848-001\u001b[39m","timestamp":"2025-07-16 01:24:36:2436"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessing log test-002 through correlation engine\u001b[39m","timestamp":"2025-07-16 01:24:36:2436"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mCorrelation engine processing completed for log test-002\u001b[39m","timestamp":"2025-07-16 01:24:36:2436"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessing log test-003 through correlation engine\u001b[39m","timestamp":"2025-07-16 01:24:36:2436"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mCorrelation engine processing completed for log test-003\u001b[39m","timestamp":"2025-07-16 01:24:36:2436"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessing log test-004 through correlation engine\u001b[39m","timestamp":"2025-07-16 01:24:36:2436"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mCorrelation engine processing completed for log test-004\u001b[39m","timestamp":"2025-07-16 01:24:36:2436"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessing log test-005 through correlation engine\u001b[39m","timestamp":"2025-07-16 01:24:36:2436"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mNo recipients configured for email alert: Multiple Failed Login Attempts\u001b[39m","timestamp":"2025-07-16 01:24:36:2436"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAlert triggered: Multiple Failed Login Attempts (high)\u001b[39m","timestamp":"2025-07-16 01:24:36:2436"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mCorrelation engine processing completed for log test-005\u001b[39m","timestamp":"2025-07-16 01:24:36:2436"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 5 logs, failed 0 logs from agent 6855a02042f295c34575d31d\u001b[39m","timestamp":"2025-07-16 01:24:36:2436"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAuto-registered new agent: 6855a02042f295c34575d31d_app-server-01 (app-server-01)\u001b[39m","timestamp":"2025-07-16 01:24:38:2438"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessing log test-006 through correlation engine\u001b[39m","timestamp":"2025-07-16 01:24:38:2438"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mNo recipients configured for email alert: Multiple Failed Login Attempts\u001b[39m","timestamp":"2025-07-16 01:24:38:2438"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAlert triggered: Multiple Failed Login Attempts (high)\u001b[39m","timestamp":"2025-07-16 01:24:38:2438"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mCorrelation engine processing completed for log test-006\u001b[39m","timestamp":"2025-07-16 01:24:38:2438"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessing log test-error-1 through correlation engine\u001b[39m","timestamp":"2025-07-16 01:24:38:2438"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mNo recipients configured for email alert: Multiple Failed Login Attempts\u001b[39m","timestamp":"2025-07-16 01:24:38:2438"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAlert triggered: Multiple Failed Login Attempts (high)\u001b[39m","timestamp":"2025-07-16 01:24:38:2438"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mCorrelation engine processing completed for log test-error-1\u001b[39m","timestamp":"2025-07-16 01:24:38:2438"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessing log test-error-2 through correlation engine\u001b[39m","timestamp":"2025-07-16 01:24:38:2438"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mNo recipients configured for email alert: Multiple Failed Login Attempts\u001b[39m","timestamp":"2025-07-16 01:24:38:2438"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAlert triggered: Multiple Failed Login Attempts (high)\u001b[39m","timestamp":"2025-07-16 01:24:38:2438"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mCorrelation engine processing completed for log test-error-2\u001b[39m","timestamp":"2025-07-16 01:24:38:2438"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessing log test-error-3 through correlation engine\u001b[39m","timestamp":"2025-07-16 01:24:38:2438"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mNo recipients configured for email alert: Multiple Failed Login Attempts\u001b[39m","timestamp":"2025-07-16 01:24:38:2438"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAlert triggered: Multiple Failed Login Attempts (high)\u001b[39m","timestamp":"2025-07-16 01:24:38:2438"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mCorrelation engine processing completed for log test-error-3\u001b[39m","timestamp":"2025-07-16 01:24:38:2438"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessing log test-error-4 through correlation engine\u001b[39m","timestamp":"2025-07-16 01:24:38:2438"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mNo recipients configured for email alert: Multiple Failed Login Attempts\u001b[39m","timestamp":"2025-07-16 01:24:38:2438"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAlert triggered: Multiple Failed Login Attempts (high)\u001b[39m","timestamp":"2025-07-16 01:24:38:2438"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mCorrelation engine processing completed for log test-error-4\u001b[39m","timestamp":"2025-07-16 01:24:38:2438"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 5 logs, failed 0 logs from agent 6855a02042f295c34575d31d\u001b[39m","timestamp":"2025-07-16 01:24:38:2438"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAuto-registered new agent: 6855a02042f295c34575d31d_app-server-02 (app-server-02)\u001b[39m","timestamp":"2025-07-16 01:24:40:2440"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessing log test-error-5 through correlation engine\u001b[39m","timestamp":"2025-07-16 01:24:40:2440"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mNo recipients configured for email alert: Multiple Failed Login Attempts\u001b[39m","timestamp":"2025-07-16 01:24:40:2440"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAlert triggered: Multiple Failed Login Attempts (high)\u001b[39m","timestamp":"2025-07-16 01:24:40:2440"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mCorrelation engine processing completed for log test-error-5\u001b[39m","timestamp":"2025-07-16 01:24:40:2440"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessing log test-error-6 through correlation engine\u001b[39m","timestamp":"2025-07-16 01:24:40:2440"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mNo recipients configured for email alert: Multiple Failed Login Attempts\u001b[39m","timestamp":"2025-07-16 01:24:40:2440"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAlert triggered: Multiple Failed Login Attempts (high)\u001b[39m","timestamp":"2025-07-16 01:24:40:2440"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mCorrelation engine processing completed for log test-error-6\u001b[39m","timestamp":"2025-07-16 01:24:40:2440"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessing log test-error-7 through correlation engine\u001b[39m","timestamp":"2025-07-16 01:24:40:2440"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mNo recipients configured for email alert: Multiple Failed Login Attempts\u001b[39m","timestamp":"2025-07-16 01:24:40:2440"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAlert triggered: Multiple Failed Login Attempts (high)\u001b[39m","timestamp":"2025-07-16 01:24:40:2440"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mCorrelation engine processing completed for log test-error-7\u001b[39m","timestamp":"2025-07-16 01:24:40:2440"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessing log test-error-8 through correlation engine\u001b[39m","timestamp":"2025-07-16 01:24:40:2440"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mNo recipients configured for email alert: Multiple Failed Login Attempts\u001b[39m","timestamp":"2025-07-16 01:24:40:2440"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAlert triggered: Multiple Failed Login Attempts (high)\u001b[39m","timestamp":"2025-07-16 01:24:40:2440"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mCorrelation engine processing completed for log test-error-8\u001b[39m","timestamp":"2025-07-16 01:24:40:2440"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessing log test-error-9 through correlation engine\u001b[39m","timestamp":"2025-07-16 01:24:40:2440"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mNo recipients configured for email alert: Multiple Failed Login Attempts\u001b[39m","timestamp":"2025-07-16 01:24:40:2440"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAlert triggered: Multiple Failed Login Attempts (high)\u001b[39m","timestamp":"2025-07-16 01:24:40:2440"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mCorrelation engine processing completed for log test-error-9\u001b[39m","timestamp":"2025-07-16 01:24:40:2440"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 5 logs, failed 0 logs from agent 6855a02042f295c34575d31d\u001b[39m","timestamp":"2025-07-16 01:24:40:2440"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUpdated agent heartbeat: 6855a02042f295c34575d31d_app-server-02 (app-server-02)\u001b[39m","timestamp":"2025-07-16 01:24:42:2442"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessing log test-error-10 through correlation engine\u001b[39m","timestamp":"2025-07-16 01:24:42:2442"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mNo recipients configured for email alert: Multiple Failed Login Attempts\u001b[39m","timestamp":"2025-07-16 01:24:43:2443"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAlert triggered: Multiple Failed Login Attempts (high)\u001b[39m","timestamp":"2025-07-16 01:24:43:2443"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mCorrelation engine processing completed for log test-error-10\u001b[39m","timestamp":"2025-07-16 01:24:43:2443"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessing log test-error-11 through correlation engine\u001b[39m","timestamp":"2025-07-16 01:24:43:2443"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mNo recipients configured for email alert: Multiple Failed Login Attempts\u001b[39m","timestamp":"2025-07-16 01:24:43:2443"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAlert triggered: Multiple Failed Login Attempts (high)\u001b[39m","timestamp":"2025-07-16 01:24:43:2443"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mCorrelation engine processing completed for log test-error-11\u001b[39m","timestamp":"2025-07-16 01:24:43:2443"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessing log test-error-12 through correlation engine\u001b[39m","timestamp":"2025-07-16 01:24:43:2443"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mNo recipients configured for email alert: Multiple Failed Login Attempts\u001b[39m","timestamp":"2025-07-16 01:24:43:2443"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAlert triggered: Multiple Failed Login Attempts (high)\u001b[39m","timestamp":"2025-07-16 01:24:43:2443"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mCorrelation engine processing completed for log test-error-12\u001b[39m","timestamp":"2025-07-16 01:24:43:2443"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessing log test-error-13 through correlation engine\u001b[39m","timestamp":"2025-07-16 01:24:43:2443"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mNo recipients configured for email alert: Multiple Failed Login Attempts\u001b[39m","timestamp":"2025-07-16 01:24:43:2443"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAlert triggered: Multiple Failed Login Attempts (high)\u001b[39m","timestamp":"2025-07-16 01:24:43:2443"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mCorrelation engine processing completed for log test-error-13\u001b[39m","timestamp":"2025-07-16 01:24:43:2443"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessing log test-error-14 through correlation engine\u001b[39m","timestamp":"2025-07-16 01:24:43:2443"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mNo recipients configured for email alert: Multiple Failed Login Attempts\u001b[39m","timestamp":"2025-07-16 01:24:43:2443"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAlert triggered: Multiple Failed Login Attempts (high)\u001b[39m","timestamp":"2025-07-16 01:24:43:2443"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mCorrelation engine processing completed for log test-error-14\u001b[39m","timestamp":"2025-07-16 01:24:43:2443"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 5 logs, failed 0 logs from agent 6855a02042f295c34575d31d\u001b[39m","timestamp":"2025-07-16 01:24:43:2443"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUpdated agent heartbeat: 6855a02042f295c34575d31d_app-server-02 (app-server-02)\u001b[39m","timestamp":"2025-07-16 01:24:45:2445"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessing log test-error-15 through correlation engine\u001b[39m","timestamp":"2025-07-16 01:24:45:2445"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mNo recipients configured for email alert: Multiple Failed Login Attempts\u001b[39m","timestamp":"2025-07-16 01:24:45:2445"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAlert triggered: Multiple Failed Login Attempts (high)\u001b[39m","timestamp":"2025-07-16 01:24:45:2445"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mCorrelation engine processing completed for log test-error-15\u001b[39m","timestamp":"2025-07-16 01:24:45:2445"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessing log test-error-16 through correlation engine\u001b[39m","timestamp":"2025-07-16 01:24:45:2445"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mNo recipients configured for email alert: Multiple Failed Login Attempts\u001b[39m","timestamp":"2025-07-16 01:24:45:2445"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAlert triggered: Multiple Failed Login Attempts (high)\u001b[39m","timestamp":"2025-07-16 01:24:45:2445"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mNo recipients configured for email alert: System Error Spike\u001b[39m","timestamp":"2025-07-16 01:24:45:2445"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAlert triggered: System Error Spike (high)\u001b[39m","timestamp":"2025-07-16 01:24:45:2445"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mCorrelation engine processing completed for log test-error-16\u001b[39m","timestamp":"2025-07-16 01:24:45:2445"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessing log test-error-17 through correlation engine\u001b[39m","timestamp":"2025-07-16 01:24:45:2445"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mNo recipients configured for email alert: Multiple Failed Login Attempts\u001b[39m","timestamp":"2025-07-16 01:24:45:2445"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAlert triggered: Multiple Failed Login Attempts (high)\u001b[39m","timestamp":"2025-07-16 01:24:45:2445"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mNo recipients configured for email alert: System Error Spike\u001b[39m","timestamp":"2025-07-16 01:24:45:2445"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAlert triggered: System Error Spike (high)\u001b[39m","timestamp":"2025-07-16 01:24:45:2445"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mCorrelation engine processing completed for log test-error-17\u001b[39m","timestamp":"2025-07-16 01:24:45:2445"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessing log test-error-18 through correlation engine\u001b[39m","timestamp":"2025-07-16 01:24:45:2445"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mNo recipients configured for email alert: Multiple Failed Login Attempts\u001b[39m","timestamp":"2025-07-16 01:24:45:2445"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAlert triggered: Multiple Failed Login Attempts (high)\u001b[39m","timestamp":"2025-07-16 01:24:45:2445"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mNo recipients configured for email alert: System Error Spike\u001b[39m","timestamp":"2025-07-16 01:24:45:2445"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAlert triggered: System Error Spike (high)\u001b[39m","timestamp":"2025-07-16 01:24:45:2445"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mCorrelation engine processing completed for log test-error-18\u001b[39m","timestamp":"2025-07-16 01:24:45:2445"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessing log test-error-19 through correlation engine\u001b[39m","timestamp":"2025-07-16 01:24:45:2445"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mNo recipients configured for email alert: Multiple Failed Login Attempts\u001b[39m","timestamp":"2025-07-16 01:24:45:2445"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAlert triggered: Multiple Failed Login Attempts (high)\u001b[39m","timestamp":"2025-07-16 01:24:45:2445"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mNo recipients configured for email alert: System Error Spike\u001b[39m","timestamp":"2025-07-16 01:24:45:2445"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAlert triggered: System Error Spike (high)\u001b[39m","timestamp":"2025-07-16 01:24:45:2445"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mCorrelation engine processing completed for log test-error-19\u001b[39m","timestamp":"2025-07-16 01:24:45:2445"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 5 logs, failed 0 logs from agent 6855a02042f295c34575d31d\u001b[39m","timestamp":"2025-07-16 01:24:45:2445"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUpdated agent heartbeat: 6855a02042f295c34575d31d_app-server-02 (app-server-02)\u001b[39m","timestamp":"2025-07-16 01:24:47:2447"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessing log test-error-20 through correlation engine\u001b[39m","timestamp":"2025-07-16 01:24:47:2447"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mNo recipients configured for email alert: Multiple Failed Login Attempts\u001b[39m","timestamp":"2025-07-16 01:24:47:2447"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAlert triggered: Multiple Failed Login Attempts (high)\u001b[39m","timestamp":"2025-07-16 01:24:47:2447"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mNo recipients configured for email alert: System Error Spike\u001b[39m","timestamp":"2025-07-16 01:24:47:2447"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAlert triggered: System Error Spike (high)\u001b[39m","timestamp":"2025-07-16 01:24:47:2447"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mCorrelation engine processing completed for log test-error-20\u001b[39m","timestamp":"2025-07-16 01:24:47:2447"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessing log test-error-21 through correlation engine\u001b[39m","timestamp":"2025-07-16 01:24:47:2447"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mNo recipients configured for email alert: Multiple Failed Login Attempts\u001b[39m","timestamp":"2025-07-16 01:24:47:2447"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAlert triggered: Multiple Failed Login Attempts (high)\u001b[39m","timestamp":"2025-07-16 01:24:47:2447"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mNo recipients configured for email alert: System Error Spike\u001b[39m","timestamp":"2025-07-16 01:24:47:2447"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAlert triggered: System Error Spike (high)\u001b[39m","timestamp":"2025-07-16 01:24:47:2447"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mCorrelation engine processing completed for log test-error-21\u001b[39m","timestamp":"2025-07-16 01:24:47:2447"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessing log test-error-22 through correlation engine\u001b[39m","timestamp":"2025-07-16 01:24:47:2447"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mNo recipients configured for email alert: Multiple Failed Login Attempts\u001b[39m","timestamp":"2025-07-16 01:24:47:2447"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAlert triggered: Multiple Failed Login Attempts (high)\u001b[39m","timestamp":"2025-07-16 01:24:47:2447"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mNo recipients configured for email alert: System Error Spike\u001b[39m","timestamp":"2025-07-16 01:24:47:2447"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAlert triggered: System Error Spike (high)\u001b[39m","timestamp":"2025-07-16 01:24:47:2447"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mCorrelation engine processing completed for log test-error-22\u001b[39m","timestamp":"2025-07-16 01:24:47:2447"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessing log test-error-23 through correlation engine\u001b[39m","timestamp":"2025-07-16 01:24:47:2447"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mNo recipients configured for email alert: Multiple Failed Login Attempts\u001b[39m","timestamp":"2025-07-16 01:24:47:2447"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAlert triggered: Multiple Failed Login Attempts (high)\u001b[39m","timestamp":"2025-07-16 01:24:47:2447"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mNo recipients configured for email alert: System Error Spike\u001b[39m","timestamp":"2025-07-16 01:24:47:2447"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAlert triggered: System Error Spike (high)\u001b[39m","timestamp":"2025-07-16 01:24:47:2447"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mCorrelation engine processing completed for log test-error-23\u001b[39m","timestamp":"2025-07-16 01:24:47:2447"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessing log test-error-24 through correlation engine\u001b[39m","timestamp":"2025-07-16 01:24:47:2447"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mNo recipients configured for email alert: Multiple Failed Login Attempts\u001b[39m","timestamp":"2025-07-16 01:24:47:2447"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAlert triggered: Multiple Failed Login Attempts (high)\u001b[39m","timestamp":"2025-07-16 01:24:47:2447"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mNo recipients configured for email alert: System Error Spike\u001b[39m","timestamp":"2025-07-16 01:24:47:2447"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAlert triggered: System Error Spike (high)\u001b[39m","timestamp":"2025-07-16 01:24:47:2447"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mCorrelation engine processing completed for log test-error-24\u001b[39m","timestamp":"2025-07-16 01:24:47:2447"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 5 logs, failed 0 logs from agent 6855a02042f295c34575d31d\u001b[39m","timestamp":"2025-07-16 01:24:47:2447"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUpdated agent heartbeat: 6855a02042f295c34575d31d_app-server-02 (app-server-02)\u001b[39m","timestamp":"2025-07-16 01:24:49:2449"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessing log test-error-25 through correlation engine\u001b[39m","timestamp":"2025-07-16 01:24:49:2449"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mNo recipients configured for email alert: Multiple Failed Login Attempts\u001b[39m","timestamp":"2025-07-16 01:24:49:2449"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAlert triggered: Multiple Failed Login Attempts (high)\u001b[39m","timestamp":"2025-07-16 01:24:49:2449"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mNo recipients configured for email alert: System Error Spike\u001b[39m","timestamp":"2025-07-16 01:24:49:2449"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAlert triggered: System Error Spike (high)\u001b[39m","timestamp":"2025-07-16 01:24:49:2449"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mCorrelation engine processing completed for log test-error-25\u001b[39m","timestamp":"2025-07-16 01:24:49:2449"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessing log test-007 through correlation engine\u001b[39m","timestamp":"2025-07-16 01:24:49:2449"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mNo recipients configured for email alert: Multiple Failed Login Attempts\u001b[39m","timestamp":"2025-07-16 01:24:49:2449"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAlert triggered: Multiple Failed Login Attempts (high)\u001b[39m","timestamp":"2025-07-16 01:24:49:2449"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mNo recipients configured for email alert: System Error Spike\u001b[39m","timestamp":"2025-07-16 01:24:49:2449"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAlert triggered: System Error Spike (high)\u001b[39m","timestamp":"2025-07-16 01:24:49:2449"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mCorrelation engine processing completed for log test-007\u001b[39m","timestamp":"2025-07-16 01:24:49:2449"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessing log test-008 through correlation engine\u001b[39m","timestamp":"2025-07-16 01:24:50:2450"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mNo recipients configured for email alert: Multiple Failed Login Attempts\u001b[39m","timestamp":"2025-07-16 01:24:50:2450"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAlert triggered: Multiple Failed Login Attempts (high)\u001b[39m","timestamp":"2025-07-16 01:24:50:2450"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mNo recipients configured for email alert: System Error Spike\u001b[39m","timestamp":"2025-07-16 01:24:50:2450"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAlert triggered: System Error Spike (high)\u001b[39m","timestamp":"2025-07-16 01:24:50:2450"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mCorrelation engine processing completed for log test-008\u001b[39m","timestamp":"2025-07-16 01:24:50:2450"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessing log test-009 through correlation engine\u001b[39m","timestamp":"2025-07-16 01:24:50:2450"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mNo recipients configured for email alert: Multiple Failed Login Attempts\u001b[39m","timestamp":"2025-07-16 01:24:50:2450"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAlert triggered: Multiple Failed Login Attempts (high)\u001b[39m","timestamp":"2025-07-16 01:24:50:2450"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mNo recipients configured for email alert: System Error Spike\u001b[39m","timestamp":"2025-07-16 01:24:50:2450"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAlert triggered: System Error Spike (high)\u001b[39m","timestamp":"2025-07-16 01:24:50:2450"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mCorrelation engine processing completed for log test-009\u001b[39m","timestamp":"2025-07-16 01:24:50:2450"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessing log test-010 through correlation engine\u001b[39m","timestamp":"2025-07-16 01:24:50:2450"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mNo recipients configured for email alert: Multiple Failed Login Attempts\u001b[39m","timestamp":"2025-07-16 01:24:50:2450"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAlert triggered: Multiple Failed Login Attempts (high)\u001b[39m","timestamp":"2025-07-16 01:24:50:2450"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mNo recipients configured for email alert: System Error Spike\u001b[39m","timestamp":"2025-07-16 01:24:50:2450"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAlert triggered: System Error Spike (high)\u001b[39m","timestamp":"2025-07-16 01:24:50:2450"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mCorrelation engine processing completed for log test-010\u001b[39m","timestamp":"2025-07-16 01:24:50:2450"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 5 logs, failed 0 logs from agent 6855a02042f295c34575d31d\u001b[39m","timestamp":"2025-07-16 01:24:50:2450"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAlert rules changed, reloading...\u001b[39m","timestamp":"2025-07-16 01:24:55:2455"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mLoaded 8 alert rules into correlation engine\u001b[39m","timestamp":"2025-07-16 01:24:55:2455"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mLoaded 7 alert rules into correlation engine\u001b[39m","timestamp":"2025-07-16 01:25:16:2516"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAlert rules changed, reloading...\u001b[39m","timestamp":"2025-07-16 01:25:25:2525"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mLoaded 7 alert rules into correlation engine\u001b[39m","timestamp":"2025-07-16 01:25:25:2525"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUpdated agent statuses: 0 online, 3 warning, 0 offline\u001b[39m","timestamp":"2025-07-16 01:26:55:2655"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in successfully: <EMAIL> from IP: ************\u001b[39m","timestamp":"2025-07-16 01:28:38:2838"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mEmail service not initialized: missing configuration\u001b[39m","timestamp":"2025-07-16 01:28:38:2838"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to send login notification <NAME_EMAIL>: Email service is not properly configured\u001b[39m","stack":"Error: Email service is not properly configured\n    at EmailService.sendEmail (/app/src/services/emailService.js:93:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async EmailService.sendTemplatedEmail (/app/src/services/emailService.js:335:12)\n    at async EmailService.sendLoginNotificationEmail (/app/src/services/emailService.js:361:12)\n    at async Immediate.<anonymous> (/app/src/routes/auth.js:355:9)","timestamp":"2025-07-16 01:28:38:2838"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUpdated agent statuses: 0 online, 0 warning, 3 offline\u001b[39m","timestamp":"2025-07-16 01:34:55:3455"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mNo recipients configured for agent status notifications\u001b[39m","timestamp":"2025-07-16 01:34:55:3455"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mReceived SIGTERM, shutting down WebSocket server...\u001b[39m","timestamp":"2025-07-16 03:21:53:2153"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mReceived SIGTERM. Starting graceful shutdown...\u001b[39m","timestamp":"2025-07-16 03:21:53:2153"}
