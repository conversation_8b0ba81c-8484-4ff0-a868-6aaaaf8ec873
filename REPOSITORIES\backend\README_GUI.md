# Python Logging Agent - Modern GUI Edition

A modern, user-friendly cybersecurity log collection and standardization agent with a beautiful graphical interface built using Flet.

## 🚀 Features

### Modern GUI Interface
- **Intuitive Design**: Clean, modern interface built with <PERSON>let (Flutter for Python)
- **Real-time Dashboard**: Live system monitoring and service status
- **Easy Configuration**: User-friendly forms for API keys, endpoints, and settings
- **Service Management**: Install, start, stop, and monitor the Windows service
- **Live Logs Viewer**: Real-time log viewing with filtering and search
- **Responsive Layout**: Adapts to different screen sizes

### Core Functionality
- **Multi-Source Collection**: Windows Event Logs, application logs, network traffic
- **Real-time Processing**: Configurable processing intervals
- **API Integration**: Seamless integration with ExLog dashboard
- **Windows Service**: Runs continuously in the background
- **Security Focused**: Designed for cybersecurity monitoring

### Easy Distribution
- **MSI Installer**: Single-file installer for easy deployment
- **Automated Setup**: Service installation and configuration
- **Start Menu Integration**: Professional shortcuts and organization
- **No Manual Configuration**: Works out of the box

## 📦 Installation

### For End Users

1. **Download the MSI installer** from the releases page
2. **Run as Administrator**: Right-click the MSI file and select "Run as administrator"
3. **Follow the installation wizard**
4. **Launch the GUI** from Start Menu → Python Logging Agent → Logging Agent GUI
5. **Configure your settings** in the Configuration tab
6. **Start the service** from the Service Control tab

### For Developers

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd backend
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements-gui.txt
   ```

3. **Run the GUI in development mode**
   ```bash
   python launch_gui.py
   ```

## 🔧 Building the MSI Installer

To create your own MSI installer:

1. **Install build dependencies**
   ```bash
   pip install cx-Freeze pywin32
   ```

2. **Run the automated build script**
   ```bash
   python build_msi.py
   ```

3. **Find your MSI** in the `dist/` directory

The build script will:
- Check all dependencies
- Clean previous builds
- Create the executable
- Generate the MSI installer
- Validate the output

## 🎯 Usage Guide

### First Time Setup

1. **Launch the GUI** from the Start Menu
2. **Go to Configuration tab**
3. **Enter your API settings**:
   - API Key: Your ExLog API key
   - Endpoint: Your ExLog server URL
   - Batch Size: Number of logs per batch (default: 10)
   - Timeout: API request timeout (default: 30s)
4. **Configure general settings**:
   - Log Level: Application logging level
   - Processing Interval: How often to process logs
5. **Save Configuration**
6. **Test Connection** to verify settings

### Service Management

1. **Go to Service Control tab**
2. **Install Service** (if not already installed)
3. **Start Service** to begin log collection
4. **Monitor status** in real-time

### Monitoring

1. **Dashboard tab** shows:
   - Service status
   - System performance metrics
   - CPU, memory, and disk usage
   - System uptime
2. **Logs tab** displays:
   - Real-time application logs
   - Filterable by log level
   - Auto-refresh capability

## 🛠️ Configuration

### API Configuration
- **API Key**: Authentication key for ExLog dashboard
- **Endpoint**: Full URL to the ExLog API endpoint
- **Batch Size**: Number of logs to send in each batch
- **Timeout**: Maximum time to wait for API responses

### Collection Settings
The agent collects from multiple sources:
- **Windows Event Logs**: System, Application, Security logs
- **Application Logs**: Custom application logging
- **Network Logs**: Network connection monitoring
- **System Metrics**: Performance and health data

### Output Options
- **API Output**: Send to ExLog dashboard (primary)
- **File Output**: Local JSON file storage
- **Console Output**: Debug output to console

## 🔍 Troubleshooting

### Common Issues

**Service won't start**
- Ensure you're running as Administrator
- Check Windows Event Viewer for service errors
- Verify configuration file is valid

**GUI won't launch**
- Check if all dependencies are installed
- Look for error messages in the console
- Verify Python path is correct

**API connection fails**
- Verify API key and endpoint URL
- Check network connectivity
- Test with the "Test Connection" button

### Log Files

Application logs are stored in the `logs/` directory:
- `gui_YYYYMMDD.log`: GUI application logs
- `service.log`: Windows service logs
- `agent.log`: Main agent logs
- `agent_errors.log`: Error-specific logs

### Debug Mode

For development and troubleshooting:
```bash
python launch_gui.py
```

## 🏗️ Architecture

### Components

- **GUI Application** (`gui/`): Modern Flet-based interface
- **Core Agent** (`logging_agent/`): Log collection engine
- **Windows Service** (`service/`): Background service wrapper
- **Configuration** (`config/`): Settings management
- **Utilities** (`utils/`): Helper functions and classes

### Technology Stack

- **Frontend**: Flet (Flutter for Python)
- **Backend**: Python 3.8+
- **Service**: Windows Service API (pywin32)
- **Packaging**: cx_Freeze + MSI
- **Configuration**: YAML
- **Logging**: Python logging module

## 📋 Requirements

### System Requirements
- Windows 10 or later
- Administrator privileges (for service installation)
- 100MB disk space
- 256MB RAM minimum

### Python Requirements (for development)
- Python 3.8 or later
- See `requirements-gui.txt` for complete list

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

Proprietary - Internal use only

## 🆘 Support

For technical support:
- Check the troubleshooting section
- Review log files for errors
- Contact the security team
- Refer to the ExLog dashboard documentation

---

**Built with ❤️ by the Security Team**
