"""
Modern theme configuration for the Logging Agent GUI
"""

import flet as ft

class AppTheme:
    """Modern theme configuration"""
    
    # Color palette
    PRIMARY_COLOR = "#2563eb"  # Blue
    PRIMARY_VARIANT = "#1d4ed8"
    SECONDARY_COLOR = "#10b981"  # Green
    SECONDARY_VARIANT = "#059669"
    ERROR_COLOR = "#ef4444"  # Red
    WARNING_COLOR = "#f59e0b"  # Amber
    SUCCESS_COLOR = "#10b981"  # Green
    INFO_COLOR = "#3b82f6"  # Blue
    
    # Background colors
    SURFACE_COLOR = "#ffffff"
    BACKGROUND_COLOR = "#f8fafc"
    CARD_COLOR = "#ffffff"
    
    # Text colors
    ON_PRIMARY = "#ffffff"
    ON_SURFACE = "#1e293b"
    ON_BACKGROUND = "#334155"
    
    # Border and divider
    OUTLINE_COLOR = "#e2e8f0"
    DIVIDER_COLOR = "#f1f5f9"
    
    def get_theme(self) -> ft.Theme:
        """Get the configured theme"""
        return ft.Theme(
            color_scheme=ft.ColorScheme(
                primary=self.PRIMARY_COLOR,
                primary_container=self.PRIMARY_VARIANT,
                secondary=self.SECONDARY_COLOR,
                secondary_container=self.SECONDARY_VARIANT,
                surface=self.SURFACE_COLOR,
                background=self.BACKGROUND_COLOR,
                error=self.ERROR_COLOR,
                on_primary=self.ON_PRIMARY,
                on_surface=self.ON_SURFACE,
                on_background=self.ON_BACKGROUND,
                outline=self.OUTLINE_COLOR,
            ),
            text_theme=ft.TextTheme(
                display_large=ft.TextStyle(
                    size=32,
                    weight=ft.FontWeight.BOLD,
                    color=self.ON_SURFACE
                ),
                display_medium=ft.TextStyle(
                    size=28,
                    weight=ft.FontWeight.BOLD,
                    color=self.ON_SURFACE
                ),
                display_small=ft.TextStyle(
                    size=24,
                    weight=ft.FontWeight.BOLD,
                    color=self.ON_SURFACE
                ),
                headline_large=ft.TextStyle(
                    size=22,
                    weight=ft.FontWeight.W_600,
                    color=self.ON_SURFACE
                ),
                headline_medium=ft.TextStyle(
                    size=20,
                    weight=ft.FontWeight.W_600,
                    color=self.ON_SURFACE
                ),
                headline_small=ft.TextStyle(
                    size=18,
                    weight=ft.FontWeight.W_600,
                    color=self.ON_SURFACE
                ),
                title_large=ft.TextStyle(
                    size=16,
                    weight=ft.FontWeight.W_500,
                    color=self.ON_SURFACE
                ),
                title_medium=ft.TextStyle(
                    size=14,
                    weight=ft.FontWeight.W_500,
                    color=self.ON_SURFACE
                ),
                title_small=ft.TextStyle(
                    size=12,
                    weight=ft.FontWeight.W_500,
                    color=self.ON_SURFACE
                ),
                body_large=ft.TextStyle(
                    size=14,
                    color=self.ON_BACKGROUND
                ),
                body_medium=ft.TextStyle(
                    size=12,
                    color=self.ON_BACKGROUND
                ),
                body_small=ft.TextStyle(
                    size=11,
                    color=self.ON_BACKGROUND
                ),
            )
        )
    
    @staticmethod
    def get_card_style() -> dict:
        """Get card styling"""
        return {
            "bgcolor": AppTheme.CARD_COLOR,
            "border": ft.border.all(1, AppTheme.OUTLINE_COLOR),
            "border_radius": 12,
            "padding": 20,
            "shadow": ft.BoxShadow(
                spread_radius=0,
                blur_radius=4,
                color="#00000010",
                offset=ft.Offset(0, 2)
            )
        }
    
    @staticmethod
    def get_button_style(variant: str = "primary") -> dict:
        """Get button styling"""
        styles = {
            "primary": {
                "bgcolor": AppTheme.PRIMARY_COLOR,
                "color": AppTheme.ON_PRIMARY,
                "elevation": 2
            },
            "secondary": {
                "bgcolor": AppTheme.SECONDARY_COLOR,
                "color": AppTheme.ON_PRIMARY,
                "elevation": 2
            },
            "outline": {
                "bgcolor": "transparent",
                "color": AppTheme.PRIMARY_COLOR,
                "elevation": 0
            },
            "error": {
                "bgcolor": AppTheme.ERROR_COLOR,
                "color": AppTheme.ON_PRIMARY,
                "elevation": 2
            }
        }
        return styles.get(variant, styles["primary"])
    
    @staticmethod
    def get_status_color(status: str) -> str:
        """Get color for status indicators"""
        status_colors = {
            "running": AppTheme.SUCCESS_COLOR,
            "stopped": AppTheme.ERROR_COLOR,
            "starting": AppTheme.WARNING_COLOR,
            "stopping": AppTheme.WARNING_COLOR,
            "error": AppTheme.ERROR_COLOR,
            "unknown": "#6b7280"
        }
        return status_colors.get(status.lower(), "#6b7280")
