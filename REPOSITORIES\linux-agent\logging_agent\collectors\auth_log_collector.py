"""
Authentication Log Collector for Linux Log Collection Agent

This module collects authentication logs from /var/log/auth.log, /var/log/secure,
and other authentication-related log files on Linux systems.
"""

import logging
import os
import re
from datetime import datetime
from typing import Dict, List, Any, Optional

from .base_collector import BaseLogCollector


class AuthLogCollector(BaseLogCollector):
    """Collector for Linux authentication logs."""
    
    def __init__(self, config: Dict[str, Any], logger: Optional[logging.Logger] = None):
        """
        Initialize the auth log collector.
        
        Args:
            config: Collector configuration
            logger: Logger instance
        """
        super().__init__(config, logger)
        
        # Auth-specific configuration
        self.real_time = config.get('real_time', True)
        
        # Default auth log paths if none specified
        if not config.get('paths'):
            self.config['paths'] = self._get_default_auth_paths()
        
        self.logger.info(f"Auth log collector initialized with {len(self.config['paths'])} paths")
    
    def _get_default_auth_paths(self) -> List[str]:
        """Get default authentication log paths for different distributions."""
        default_paths = [
            '/var/log/auth.log',    # Ubuntu, Debian
            '/var/log/secure',      # CentOS, RHEL, Fedora
            '/var/log/authlog'      # Some other distributions
        ]
        
        existing_paths = []
        for path in default_paths:
            if os.path.exists(path) and os.access(path, os.R_OK):
                existing_paths.append(path)
        
        return existing_paths
    
    def collect_logs(self) -> List[Dict[str, Any]]:
        """
        Collect logs from authentication log files.
        
        Returns:
            List of collected log entries
        """
        collected_logs = []
        
        try:
            log_paths = self._get_log_paths()
            
            if not log_paths:
                self.logger.warning("No accessible authentication log files found")
                return collected_logs
            
            for log_path in log_paths:
                try:
                    # Read new lines from the file
                    new_lines = self._read_file_from_position(log_path)
                    
                    if new_lines:
                        self.logger.debug(f"Processing {len(new_lines)} new auth log lines from {log_path}")
                        
                        for line in new_lines:
                            log_entry = self._parse_auth_log_line(line)
                            if log_entry:
                                # Add file-specific metadata
                                log_entry['additionalFields']['log_file'] = log_path
                                log_entry['additionalFields']['collector'] = 'auth'
                                
                                # Sanitize message
                                log_entry['message'] = self._sanitize_message(log_entry['message'])
                                
                                collected_logs.append(log_entry)
                        
                        self.stats['files_processed'] += 1
                
                except Exception as e:
                    self.logger.error(f"Error processing auth log file {log_path}: {e}")
                    self.stats['errors'] += 1
            
            if collected_logs:
                self.stats['logs_collected'] += len(collected_logs)
                self.stats['last_collection'] = datetime.now()
                self.logger.debug(f"Collected {len(collected_logs)} auth log entries")
            
        except Exception as e:
            self.logger.error(f"Error in auth log collection: {e}")
            self.stats['errors'] += 1
        
        return collected_logs
    
    def _parse_auth_log_line(self, line: str) -> Optional[Dict[str, Any]]:
        """
        Parse an authentication log line with enhanced auth-specific parsing.
        
        Args:
            line: Log line to parse
            
        Returns:
            Parsed log entry or None if parsing failed
        """
        # Use base class parsing first
        log_entry = self._parse_syslog_line(line, source="Auth")
        
        if not log_entry:
            return None
        
        # Set source type to auth
        log_entry['sourceType'] = 'auth'

        # Enhanced authentication-specific processing
        try:
            message = log_entry['message']
            additional_fields = log_entry['additionalFields']
            
            # Extract authentication information
            auth_info = self._extract_auth_info(message)
            if auth_info:
                additional_fields.update(auth_info)
            
            # Classify authentication event
            auth_event_type = self._classify_auth_event(message)
            if auth_event_type:
                additional_fields['auth_event_type'] = auth_event_type
                
                # Adjust log level based on event type
                if auth_event_type in ['failed_login', 'invalid_user', 'authentication_failure']:
                    log_entry['logLevel'] = 'warning'
                elif auth_event_type in ['successful_login', 'session_opened']:
                    log_entry['logLevel'] = 'info'
                elif auth_event_type in ['brute_force', 'account_locked']:
                    log_entry['logLevel'] = 'error'
            
            # Extract user information
            user_info = self._extract_user_info(message)
            if user_info:
                additional_fields.update(user_info)
            
            # Extract connection information
            connection_info = self._extract_connection_info(message)
            if connection_info:
                additional_fields['connection'] = connection_info
            
            # Extract sudo/privilege escalation information
            sudo_info = self._extract_sudo_info(message)
            if sudo_info:
                additional_fields['sudo'] = sudo_info
            
        except Exception as e:
            self.logger.error(f"Error in enhanced auth log parsing: {e}")
        
        return log_entry
    
    def _extract_auth_info(self, message: str) -> Optional[Dict[str, Any]]:
        """Extract authentication-related information from message."""
        auth_info = {}
        message_lower = message.lower()
        
        # Authentication methods
        if 'password' in message_lower:
            auth_info['auth_method'] = 'password'
        elif 'publickey' in message_lower or 'key' in message_lower:
            auth_info['auth_method'] = 'publickey'
        elif 'pam' in message_lower:
            auth_info['auth_method'] = 'pam'
        
        # Authentication services
        if 'sshd' in message_lower:
            auth_info['auth_service'] = 'ssh'
        elif 'login' in message_lower:
            auth_info['auth_service'] = 'login'
        elif 'su' in message_lower:
            auth_info['auth_service'] = 'su'
        elif 'sudo' in message_lower:
            auth_info['auth_service'] = 'sudo'
        
        return auth_info if auth_info else None
    
    def _classify_auth_event(self, message: str) -> Optional[str]:
        """Classify the type of authentication event."""
        message_lower = message.lower()
        
        # Failed authentication attempts
        if any(fail in message_lower for fail in ['failed', 'failure', 'invalid', 'incorrect']):
            if 'password' in message_lower:
                return 'failed_login'
            elif 'user' in message_lower:
                return 'invalid_user'
            else:
                return 'authentication_failure'
        
        # Successful authentication
        if any(success in message_lower for success in ['accepted', 'opened', 'successful']):
            if 'session' in message_lower:
                return 'session_opened'
            else:
                return 'successful_login'
        
        # Session events
        if 'session closed' in message_lower or 'disconnected' in message_lower:
            return 'session_closed'
        
        # Privilege escalation
        if 'sudo' in message_lower:
            if 'command' in message_lower:
                return 'sudo_command'
            else:
                return 'sudo_auth'
        
        # Account management
        if any(acct in message_lower for acct in ['account', 'user']):
            if 'locked' in message_lower:
                return 'account_locked'
            elif 'created' in message_lower:
                return 'account_created'
            elif 'deleted' in message_lower:
                return 'account_deleted'
        
        # Security events
        if any(sec in message_lower for sec in ['break-in', 'attack', 'intrusion']):
            return 'security_event'
        
        # Multiple failed attempts (potential brute force)
        if re.search(r'(\d+)\s+failures?', message_lower):
            return 'brute_force'
        
        return None
    
    def _extract_user_info(self, message: str) -> Optional[Dict[str, Any]]:
        """Extract user-related information from message."""
        user_info = {}
        
        # Extract username patterns
        user_patterns = [
            r'user\s+(\w+)',
            r'for\s+(\w+)\s+from',
            r'(\w+)@',
            r'USER=(\w+)',
            r'as\s+(\w+)'
        ]
        
        for pattern in user_patterns:
            match = re.search(pattern, message, re.IGNORECASE)
            if match:
                user_info['username'] = match.group(1)
                break
        
        # Extract UID if present
        uid_match = re.search(r'uid=(\d+)', message)
        if uid_match:
            user_info['uid'] = int(uid_match.group(1))
        
        # Extract GID if present
        gid_match = re.search(r'gid=(\d+)', message)
        if gid_match:
            user_info['gid'] = int(gid_match.group(1))
        
        return user_info if user_info else None
    
    def _extract_connection_info(self, message: str) -> Optional[Dict[str, Any]]:
        """Extract connection-related information from message."""
        connection_info = {}
        
        # Extract source IP
        ip_pattern = r'from\s+(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'
        ip_match = re.search(ip_pattern, message)
        if ip_match:
            connection_info['source_ip'] = ip_match.group(1)
        
        # Extract port
        port_pattern = r'port\s+(\d+)'
        port_match = re.search(port_pattern, message)
        if port_match:
            connection_info['source_port'] = int(port_match.group(1))
        
        # Extract hostname
        hostname_pattern = r'from\s+([a-zA-Z0-9.-]+)'
        hostname_match = re.search(hostname_pattern, message)
        if hostname_match and not re.match(r'\d+\.\d+\.\d+\.\d+', hostname_match.group(1)):
            connection_info['source_hostname'] = hostname_match.group(1)
        
        # Extract TTY/PTY
        tty_pattern = r'(tty|pty)(\w+)'
        tty_match = re.search(tty_pattern, message)
        if tty_match:
            connection_info['terminal'] = tty_match.group(0)
        
        return connection_info if connection_info else None
    
    def _extract_sudo_info(self, message: str) -> Optional[Dict[str, Any]]:
        """Extract sudo-related information from message."""
        if 'sudo' not in message.lower():
            return None
        
        sudo_info = {}
        
        # Extract command
        command_pattern = r'COMMAND=(.+)$'
        command_match = re.search(command_pattern, message)
        if command_match:
            sudo_info['command'] = command_match.group(1).strip()
        
        # Extract working directory
        pwd_pattern = r'PWD=([^\s]+)'
        pwd_match = re.search(pwd_pattern, message)
        if pwd_match:
            sudo_info['working_directory'] = pwd_match.group(1)
        
        # Extract target user
        user_pattern = r'USER=(\w+)'
        user_match = re.search(user_pattern, message)
        if user_match:
            sudo_info['target_user'] = user_match.group(1)
        
        return sudo_info if sudo_info else None
    
    def start(self) -> None:
        """Start the auth log collector."""
        super().start()
        
        # Log accessible auth log files
        log_paths = self._get_log_paths()
        if log_paths:
            self.logger.info(f"Monitoring authentication log files: {log_paths}")
        else:
            self.logger.warning("No accessible authentication log files found")
            self.logger.info("You may need to run with appropriate privileges or add user to required groups")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get auth log collector statistics."""
        stats = super().get_stats()
        stats['collector_type'] = 'auth'
        stats['monitored_files'] = self._get_log_paths()
        return stats
