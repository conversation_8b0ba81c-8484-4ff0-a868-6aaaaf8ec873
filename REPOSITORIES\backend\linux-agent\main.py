#!/usr/bin/env python3
"""
Linux Log Collection Agent - Main Entry Point

This is the main entry point for the Linux log collection agent.
It provides console and service modes for running the agent.
"""

import argparse
import os
import signal
import sys
import time
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

try:
    from logging_agent.agent import LinuxLoggingAgent
    from config.config_manager import ConfigManager
    from utils.logger import LoggerSetup
except ImportError as e:
    print(f"Error importing modules: {e}")
    print("Please ensure all dependencies are installed: pip3 install -r requirements.txt")
    sys.exit(1)


def check_permissions():
    """Check if the agent has necessary permissions to read system logs."""
    required_paths = [
        '/var/log/syslog',
        '/var/log/messages', 
        '/var/log/auth.log',
        '/var/log/secure',
        '/var/log/kern.log'
    ]
    
    accessible_paths = []
    for path in required_paths:
        if os.path.exists(path) and os.access(path, os.R_OK):
            accessible_paths.append(path)
    
    if not accessible_paths:
        print("WARNING: No system log files are accessible.")
        print("You may need to run with sudo or add the user to appropriate groups.")
        print("Required groups may include: adm, systemd-journal, syslog")
        return False
    
    print(f"Accessible log files: {accessible_paths}")
    return True


def setup_signal_handlers(agent):
    """Set up signal handlers for graceful shutdown."""
    def signal_handler(signum, frame):
        print(f"\nReceived signal {signum}, shutting down gracefully...")
        if agent:
            agent.stop()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)


def run_console_mode(config_path=None):
    """Run the agent in console mode."""
    print("Linux Log Collection Agent - Console Mode")
    print("=" * 50)
    
    # Check permissions
    if not check_permissions():
        response = input("Continue anyway? (y/N): ")
        if response.lower() != 'y':
            return 1
    
    try:
        # Initialize the agent
        print("Initializing agent...")
        agent = LinuxLoggingAgent(config_path=config_path)
        
        # Set up signal handlers
        setup_signal_handlers(agent)
        
        # Start the agent
        print("Starting logging agent...")
        if agent.start():
            print("Logging agent started successfully!")
            print("Press Ctrl+C to stop the agent")
            print("-" * 50)
            
            # Keep the main thread alive and show periodic status
            try:
                while True:
                    time.sleep(1)
                    
                    # Print status every 60 seconds
                    if int(time.time()) % 60 == 0:
                        status = agent.get_status()
                        print(f"Status: Running | Logs collected: {status['statistics']['logs_collected']} | "
                              f"Logs processed: {status['statistics']['logs_processed']} | "
                              f"Buffer size: {status['buffer_size']}")
            
            except KeyboardInterrupt:
                print("\nShutdown requested by user")
        
        else:
            print("Failed to start logging agent")
            return 1
    
    except Exception as e:
        print(f"Error running agent: {e}")
        return 1
    
    finally:
        if 'agent' in locals():
            agent.stop()
    
    return 0


def run_service_mode(config_path=None):
    """Run the agent in service mode (for systemd)."""
    try:
        from service.systemd_service import LinuxLoggingAgentService
        
        # Initialize and run the service
        service = LinuxLoggingAgentService(config_path=config_path)
        service.run()
        
    except ImportError:
        print("Service mode not available. Missing systemd integration.")
        return 1
    except Exception as e:
        print(f"Error running service: {e}")
        return 1
    
    return 0


def install_service():
    """Install the agent as a systemd service."""
    try:
        from install.installer import ServiceInstaller
        
        installer = ServiceInstaller()
        if installer.install():
            print("Service installed successfully!")
            print("Start with: sudo systemctl start linux-log-agent")
            return 0
        else:
            print("Service installation failed!")
            return 1
            
    except ImportError:
        print("Service installer not available.")
        return 1
    except Exception as e:
        print(f"Error installing service: {e}")
        return 1


def uninstall_service():
    """Uninstall the systemd service."""
    try:
        from install.installer import ServiceInstaller
        
        installer = ServiceInstaller()
        if installer.uninstall():
            print("Service uninstalled successfully!")
            return 0
        else:
            print("Service uninstall failed!")
            return 1
            
    except ImportError:
        print("Service installer not available.")
        return 1
    except Exception as e:
        print(f"Error uninstalling service: {e}")
        return 1


def test_configuration(config_path=None):
    """Test the configuration and API connectivity."""
    try:
        print("Testing configuration...")
        
        # Load configuration
        config_manager = ConfigManager(config_path)
        config = config_manager.load_config()
        
        print("✓ Configuration loaded successfully")
        
        # Test API connectivity
        from utils.api_client import LinuxExLogAPIClient
        
        api_config = config.get('exlog_api', {})
        if not api_config.get('enabled', False):
            print("⚠ ExLog API is disabled in configuration")
            return 0
        
        client = LinuxExLogAPIClient(api_config)
        
        # Create a test log
        test_log = {
            "log_id": "test-config-check",
            "timestamp": "2024-01-01T12:00:00Z",
            "source": "System",
            "source_type": "test",
            "host": "test-host",
            "log_level": "info",
            "message": "Configuration test log",
            "raw_data": None,
            "additional_fields": {
                "test": True
            }
        }
        
        if client.send_logs([test_log]):
            print("✓ API connectivity test successful")
        else:
            print("✗ API connectivity test failed")
            return 1
        
        print("Configuration test completed successfully!")
        return 0
        
    except Exception as e:
        print(f"Configuration test failed: {e}")
        return 1


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Linux Log Collection Agent",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python3 main.py console                    # Run in console mode
  python3 main.py service                    # Run as service (for systemd)
  python3 main.py install                    # Install systemd service
  python3 main.py uninstall                  # Uninstall systemd service
  python3 main.py test                       # Test configuration
  python3 main.py console --config /path/to/config.yaml
        """
    )
    
    parser.add_argument(
        'mode',
        choices=['console', 'service', 'install', 'uninstall', 'test'],
        help='Operation mode'
    )
    
    parser.add_argument(
        '--config',
        type=str,
        help='Path to configuration file'
    )
    
    parser.add_argument(
        '--version',
        action='version',
        version='Linux Log Collection Agent 1.0.0'
    )
    
    args = parser.parse_args()
    
    # Route to appropriate function based on mode
    if args.mode == 'console':
        return run_console_mode(args.config)
    elif args.mode == 'service':
        return run_service_mode(args.config)
    elif args.mode == 'install':
        return install_service()
    elif args.mode == 'uninstall':
        return uninstall_service()
    elif args.mode == 'test':
        return test_configuration(args.config)
    else:
        parser.print_help()
        return 1


if __name__ == '__main__':
    sys.exit(main())
