"""
About component showing application information
"""

import flet as ft
import sys
import platform
from pathlib import Path
from typing import Optional

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from gui.utils.theme import AppTheme

class About(ft.Control):
    """About component"""
    
    def __init__(self, page: ft.Page, logger):
        super().__init__()
        self.page = page
        self.logger = logger
        
        # Application info
        self.app_name = "Python Logging Agent"
        self.app_version = "2.0.0"
        self.app_description = "Advanced cybersecurity log collection and standardization agent"
        self.app_author = "Security Team"
        
    async def initialize(self):
        """Initialize the about component"""
        try:
            self.logger.info("Initializing about component")
            self.logger.info("About component initialized successfully")
        except Exception as e:
            self.logger.error(f"Error initializing about component: {e}")
            raise
    
    def _create_info_row(self, label: str, value: str) -> ft.Row:
        """Create an information row"""
        return ft.Row(
            controls=[
                ft.Text(
                    f"{label}:",
                    size=14,
                    weight=ft.FontWeight.W_500,
                    color=AppTheme.ON_SURFACE,
                    width=120
                ),
                ft.Text(
                    value,
                    size=14,
                    color=AppTheme.ON_BACKGROUND,
                    selectable=True
                )
            ],
            spacing=8
        )
    
    def _create_feature_item(self, title: str, description: str) -> ft.Container:
        """Create a feature item"""
        return ft.Container(
            content=ft.Column(
                controls=[
                    ft.Text(
                        title,
                        size=14,
                        weight=ft.FontWeight.W_500,
                        color=AppTheme.ON_SURFACE
                    ),
                    ft.Text(
                        description,
                        size=12,
                        color=AppTheme.ON_BACKGROUND,
                        opacity=0.8
                    )
                ],
                spacing=4,
                tight=True
            ),
            padding=12,
            border_radius=8,
            bgcolor=ft.colors.with_opacity(0.05, AppTheme.PRIMARY_COLOR),
            border=ft.border.all(1, ft.colors.with_opacity(0.2, AppTheme.PRIMARY_COLOR))
        )
    
    def build(self):
        """Build the about interface"""
        return ft.Container(
            content=ft.Column(
                controls=[
                    # Header
                    ft.Container(
                        content=ft.Text(
                            "About",
                            size=24,
                            weight=ft.FontWeight.BOLD,
                            color=AppTheme.ON_SURFACE
                        ),
                        padding=20
                    ),
                    
                    # Application info
                    ft.Container(
                        content=ft.Column(
                            controls=[
                                # App icon and name
                                ft.Row(
                                    controls=[
                                        ft.Icon(
                                            ft.icons.SECURITY,
                                            size=48,
                                            color=AppTheme.PRIMARY_COLOR
                                        ),
                                        ft.Column(
                                            controls=[
                                                ft.Text(
                                                    self.app_name,
                                                    size=20,
                                                    weight=ft.FontWeight.BOLD,
                                                    color=AppTheme.ON_SURFACE
                                                ),
                                                ft.Text(
                                                    f"Version {self.app_version}",
                                                    size=14,
                                                    color=AppTheme.ON_BACKGROUND
                                                )
                                            ],
                                            spacing=4
                                        )
                                    ],
                                    spacing=16,
                                    alignment=ft.MainAxisAlignment.START
                                ),
                                
                                ft.Divider(color=AppTheme.OUTLINE_COLOR),
                                
                                # Description
                                ft.Text(
                                    self.app_description,
                                    size=14,
                                    color=AppTheme.ON_BACKGROUND
                                ),
                                
                                ft.Divider(color=AppTheme.OUTLINE_COLOR),
                                
                                # System information
                                ft.Text(
                                    "System Information",
                                    size=16,
                                    weight=ft.FontWeight.W_600,
                                    color=AppTheme.ON_SURFACE
                                ),
                                
                                self._create_info_row("Operating System", f"{platform.system()} {platform.release()}"),
                                self._create_info_row("Architecture", platform.machine()),
                                self._create_info_row("Python Version", f"{sys.version.split()[0]}"),
                                self._create_info_row("Platform", platform.platform()),
                                
                                ft.Divider(color=AppTheme.OUTLINE_COLOR),
                                
                                # Author info
                                ft.Text(
                                    "Development Information",
                                    size=16,
                                    weight=ft.FontWeight.W_600,
                                    color=AppTheme.ON_SURFACE
                                ),
                                
                                self._create_info_row("Author", self.app_author),
                                self._create_info_row("Framework", "Flet (Flutter for Python)"),
                                self._create_info_row("License", "Proprietary"),
                            ],
                            spacing=12
                        ),
                        **AppTheme.get_card_style()
                    ),
                    
                    # Features
                    ft.Container(
                        content=ft.Column(
                            controls=[
                                ft.Text(
                                    "Key Features",
                                    size=18,
                                    weight=ft.FontWeight.W_600,
                                    color=AppTheme.ON_SURFACE
                                ),
                                
                                ft.Row(
                                    controls=[
                                        self._create_feature_item(
                                            "Multi-Source Collection",
                                            "Collects logs from Windows Event Logs, application logs, network traffic, and system metrics"
                                        ),
                                        self._create_feature_item(
                                            "Real-time Processing",
                                            "Processes and standardizes logs in real-time with configurable intervals"
                                        )
                                    ],
                                    spacing=16
                                ),
                                
                                ft.Row(
                                    controls=[
                                        self._create_feature_item(
                                            "API Integration",
                                            "Seamlessly integrates with ExLog dashboard for centralized log management"
                                        ),
                                        self._create_feature_item(
                                            "Windows Service",
                                            "Runs as a Windows service for continuous operation and automatic startup"
                                        )
                                    ],
                                    spacing=16
                                ),
                                
                                ft.Row(
                                    controls=[
                                        self._create_feature_item(
                                            "Modern GUI",
                                            "Intuitive graphical interface for configuration and monitoring"
                                        ),
                                        self._create_feature_item(
                                            "Security Focused",
                                            "Designed specifically for cybersecurity monitoring and threat detection"
                                        )
                                    ],
                                    spacing=16
                                )
                            ],
                            spacing=16
                        ),
                        **AppTheme.get_card_style()
                    ),
                    
                    # Support info
                    ft.Container(
                        content=ft.Column(
                            controls=[
                                ft.Text(
                                    "Support & Documentation",
                                    size=18,
                                    weight=ft.FontWeight.W_600,
                                    color=AppTheme.ON_SURFACE
                                ),
                                
                                ft.Text(
                                    "For technical support, configuration assistance, or feature requests, "
                                    "please contact the security team or refer to the documentation.",
                                    size=14,
                                    color=AppTheme.ON_BACKGROUND
                                ),
                                
                                ft.Row(
                                    controls=[
                                        ft.ElevatedButton(
                                            "View Documentation",
                                            icon=ft.icons.DESCRIPTION,
                                            on_click=lambda e: None,  # TODO: Implement
                                            **AppTheme.get_button_style("outline")
                                        ),
                                        ft.ElevatedButton(
                                            "Contact Support",
                                            icon=ft.icons.SUPPORT,
                                            on_click=lambda e: None,  # TODO: Implement
                                            **AppTheme.get_button_style("outline")
                                        )
                                    ],
                                    spacing=16
                                )
                            ],
                            spacing=12
                        ),
                        **AppTheme.get_card_style()
                    )
                ],
                spacing=20
            ),
            padding=20,
            expand=True,
            bgcolor=AppTheme.BACKGROUND_COLOR,
            scroll=ft.ScrollMode.AUTO
        )
