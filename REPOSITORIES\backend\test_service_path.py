"""
Test script to verify service path detection works correctly
"""

import sys
import os
from pathlib import Path

def test_path_detection():
    """Test the path detection logic"""
    print("Testing path detection logic...")
    print(f"sys.executable: {sys.executable}")
    print(f"sys.frozen: {getattr(sys, 'frozen', False)}")
    print(f"Current working directory: {os.getcwd()}")
    print(f"__file__: {__file__}")
    
    # Simulate the packaged executable scenario
    if getattr(sys, 'frozen', False):
        print("\nRunning as packaged executable")
        executable_path = Path(sys.executable)
        print(f"Executable path: {executable_path}")
        print(f"Executable parent: {executable_path.parent}")
        
        # Find the installation directory dynamically
        install_dir = executable_path.parent
        
        # Make sure we're not in a subdirectory like 'lib'
        current_path = install_dir
        while current_path.name in ['lib', 'library.zip'] and current_path.parent != current_path:
            print(f"Moving up from: {current_path}")
            current_path = current_path.parent
        
        install_dir = current_path
        print(f"Final installation directory: {install_dir}")
        print(f"Directory exists: {install_dir.exists()}")
        print(f"Is directory: {install_dir.is_dir()}")
        
        if install_dir.exists():
            print(f"Directory contents: {list(install_dir.iterdir())}")
    else:
        print("\nRunning as Python script")
        script_dir = Path(__file__).parent.absolute()
        project_root = script_dir
        print(f"Script directory: {script_dir}")
        print(f"Project root: {project_root}")

if __name__ == "__main__":
    test_path_detection()
