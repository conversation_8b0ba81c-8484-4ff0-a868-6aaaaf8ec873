# Changelog

All notable changes to the Linux Log Collection Agent will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.1.0] - 2025-06-20

### Changed
- **BREAKING**: Updated log field names to match dashboard schema
  - `log_id` → `logId`
  - `source_type` → `sourceType`
  - `log_level` → `logLevel`
  - `additional_fields` → `additionalFields`
- **Added**: `osType` field with value "linux" for all logs
- **Updated**: All collectors to use new field names
  - Base collector
  - Journalctl collector
  - Syslog collector
  - Auth log collector
  - Kernel log collector
  - Application log collector
- **Updated**: API client validation to use new field names
- **Updated**: Test client to use new field names
- **Updated**: Default configuration to use "event" as default source type

### Technical Details
- Modified `logging_agent/agent.py` log standardization
- Updated `utils/api_client.py` validation logic
- Updated all collector classes in `logging_agent/collectors/`
- Updated `test_api_client.py` for testing
- Updated `config/default_config.yaml` default values

### Compatibility
- This version requires dashboard version 1.1.0 or higher
- Logs sent to older dashboard versions will be rejected due to schema changes

## [Unreleased]

## [1.2.0] - 2025-06-19

### Added
- **Enhanced Log Categorization**: Intelligent categorization system with 9+ categories
  - Auth: Authentication and authorization events
  - Kernel: Kernel messages and hardware events
  - Network: Network-related events and connectivity
  - Application: Web servers, databases, and application logs
  - Security: Firewall, SELinux, and security events
  - Service: System service management events
  - Scheduler: Cron jobs and scheduled tasks
  - Hardware: USB, Bluetooth, and hardware events
  - Systemd: System management and service control
- **Improved Journalctl Collector**: Smart categorization based on content analysis
- **Performance Monitoring**: Built-in resource usage tracking and limits
- **Comprehensive Documentation**: Updated README with detailed guides and examples

### Changed
- **Configuration Optimization**: Disabled duplicate collectors to prevent log duplication
- **Default Collection Strategy**: Journalctl-based collection for better real-time performance
- **Service Shutdown Handling**: Improved signal handling and graceful shutdown process
- **Memory Usage**: Optimized memory footprint (30-100MB typical usage)

### Fixed
- **Installation Dependencies**: Fixed missing `systemd-python` build dependencies
- **Permission Issues**: Resolved file ownership and group membership problems
- **API Configuration**: Fixed configuration file synchronization issues
- **Service Timeout**: Improved systemd service shutdown timeout handling
- **Log Duplication**: Eliminated duplicate log collection from multiple sources

### Security
- **Systemd Hardening**: Enhanced security with additional systemd protection features
- **Resource Limits**: Implemented CPU and memory usage limits
- **Minimal Privileges**: Runs with dedicated user account and minimal permissions

## [1.1.0] - 2025-06-18

### Added
- **Systemd Service Integration**: Full systemd service support with proper lifecycle management
- **Real-time Log Monitoring**: Efficient real-time log collection using journalctl and inotify
- **API Client Improvements**: Enhanced retry logic and offline buffering capabilities
- **Error Handling**: Comprehensive error handling and logging throughout the system
- **Installation Script**: Automated installation script for production deployment

### Changed
- **Service Architecture**: Refactored service management for better reliability
- **Configuration Management**: Improved configuration loading and validation
- **Log Standardization**: Enhanced log format standardization for ExLog compatibility

### Fixed
- **File Permissions**: Fixed various permission-related issues during installation
- **Service Startup**: Resolved service startup failures on different Linux distributions
- **Log File Access**: Fixed access issues to system log files

## [1.0.0] - 2025-06-17

### Added
- **Initial Release**: First stable version of the Linux Log Collection Agent
- **Multi-source Collection**: Support for syslog, auth logs, kernel logs, and systemd journal
- **ExLog API Integration**: Direct integration with ExLog dashboard API
- **Linux Distribution Support**: Support for Ubuntu, Debian, CentOS, RHEL, Fedora
- **Configuration System**: Flexible YAML-based configuration
- **Batch Processing**: Efficient batch processing of log entries
- **Offline Buffering**: Local buffering when API is unavailable

### Features
- Real-time log monitoring with inotify
- Automatic log level and facility mapping
- Systemd unit extraction and processing
- Distribution-specific log path detection
- Performance monitoring and resource limits
- Comprehensive error handling and logging

---

## Version History Summary

| Version | Release Date | Key Features |
|---------|--------------|--------------|
| 1.2.0 | 2025-06-19 | Enhanced categorization, performance optimization, comprehensive fixes |
| 1.1.0 | 2025-06-18 | Systemd integration, real-time monitoring, installation automation |
| 1.0.0 | 2025-06-17 | Initial release with core functionality |

## Migration Guide

### From 1.1.0 to 1.2.0
- **Configuration**: No breaking changes, but consider reviewing categorization settings
- **Service**: Restart service to apply performance improvements: `sudo systemctl restart linux-log-agent`
- **Logs**: Enhanced categorization will automatically apply to new logs

### From 1.0.0 to 1.1.0
- **Installation**: Use new installation script for easier deployment
- **Configuration**: Update configuration file format if using custom settings
- **Service**: Migrate to systemd service for better management

## Support

For version-specific issues:
- Check the [FIXES.md](FIXES.md) file for known issues and solutions
- Review the [TROUBLESHOOTING.md](docs/TROUBLESHOOTING.md) guide
- Submit issues with version information and relevant logs
