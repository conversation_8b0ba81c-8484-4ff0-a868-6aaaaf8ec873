"""
Sidebar navigation component
"""

import flet as ft
from typing import Callable, Optional
from gui.utils.theme import AppTheme

class Sidebar(ft.Control):
    """Sidebar navigation component"""
    
    def __init__(self, on_navigation: Callable, logger):
        super().__init__()
        self.on_navigation = on_navigation
        self.logger = logger
        self.current_selection = "dashboard"
        self.nav_items = []
        
    async def initialize(self):
        """Initialize the sidebar"""
        try:
            self.logger.info("Initializing sidebar")
            
            # Define navigation items
            nav_items_config = [
                {
                    "key": "dashboard",
                    "title": "Dashboard",
                    "icon": ft.icons.DASHBOARD,
                    "description": "Overview and status"
                },
                {
                    "key": "configuration",
                    "title": "Configuration",
                    "icon": ft.icons.SETTINGS,
                    "description": "API keys and settings"
                },
                {
                    "key": "service",
                    "title": "Service Control",
                    "icon": ft.icons.PLAY_CIRCLE,
                    "description": "Start, stop, and manage service"
                },
                {
                    "key": "logs",
                    "title": "Logs",
                    "icon": ft.icons.LIST_ALT,
                    "description": "View application logs"
                },
                {
                    "key": "about",
                    "title": "About",
                    "icon": ft.icons.INFO,
                    "description": "Application information"
                }
            ]
            
            # Create navigation items
            self.nav_items = []
            for item_config in nav_items_config:
                nav_item = self._create_nav_item(item_config)
                self.nav_items.append(nav_item)
            
            self.logger.info("Sidebar initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Error initializing sidebar: {e}")
            raise
    
    def _create_nav_item(self, config: dict) -> ft.Container:
        """Create a navigation item"""
        is_selected = config["key"] == self.current_selection
        
        return ft.Container(
            content=ft.Row(
                controls=[
                    ft.Icon(
                        config["icon"],
                        size=20,
                        color=AppTheme.PRIMARY_COLOR if is_selected else AppTheme.ON_BACKGROUND
                    ),
                    ft.Column(
                        controls=[
                            ft.Text(
                                config["title"],
                                size=14,
                                weight=ft.FontWeight.W_500,
                                color=AppTheme.PRIMARY_COLOR if is_selected else AppTheme.ON_SURFACE
                            ),
                            ft.Text(
                                config["description"],
                                size=11,
                                color=AppTheme.ON_BACKGROUND,
                                opacity=0.7
                            )
                        ],
                        spacing=2,
                        expand=True
                    )
                ],
                spacing=12,
                alignment=ft.MainAxisAlignment.START
            ),
            padding=ft.padding.symmetric(horizontal=16, vertical=12),
            bgcolor=AppTheme.PRIMARY_COLOR + "10" if is_selected else ft.colors.TRANSPARENT,
            border_radius=8,
            margin=ft.margin.symmetric(horizontal=8, vertical=2),
            on_click=lambda e, key=config["key"]: self._handle_navigation(key),
            ink=True,
            data=config["key"]
        )
    
    async def _handle_navigation(self, key: str):
        """Handle navigation item click"""
        try:
            if key != self.current_selection:
                self.current_selection = key
                
                # Update visual selection
                await self._update_selection()
                
                # Notify parent
                await self.on_navigation(key)
                
        except Exception as e:
            self.logger.error(f"Error handling navigation: {e}")
    
    async def _update_selection(self):
        """Update visual selection state"""
        try:
            # Recreate nav items with updated selection
            nav_items_config = [
                {
                    "key": "dashboard",
                    "title": "Dashboard",
                    "icon": ft.icons.DASHBOARD,
                    "description": "Overview and status"
                },
                {
                    "key": "configuration",
                    "title": "Configuration",
                    "icon": ft.icons.SETTINGS,
                    "description": "API keys and settings"
                },
                {
                    "key": "service",
                    "title": "Service Control",
                    "icon": ft.icons.PLAY_CIRCLE,
                    "description": "Start, stop, and manage service"
                },
                {
                    "key": "logs",
                    "title": "Logs",
                    "icon": ft.icons.LIST_ALT,
                    "description": "View application logs"
                },
                {
                    "key": "about",
                    "title": "About",
                    "icon": ft.icons.INFO,
                    "description": "Application information"
                }
            ]
            
            # Update nav items
            for i, item_config in enumerate(nav_items_config):
                self.nav_items[i] = self._create_nav_item(item_config)
            
            await self.update_async()
            
        except Exception as e:
            self.logger.error(f"Error updating selection: {e}")
    
    def build(self):
        """Build the sidebar"""
        return ft.Container(
            content=ft.Column(
                controls=[
                    # Header
                    ft.Container(
                        content=ft.Column(
                            controls=[
                                ft.Row(
                                    controls=[
                                        ft.Icon(
                                            ft.icons.SECURITY,
                                            size=32,
                                            color=AppTheme.PRIMARY_COLOR
                                        ),
                                        ft.Column(
                                            controls=[
                                                ft.Text(
                                                    "Logging Agent",
                                                    size=16,
                                                    weight=ft.FontWeight.BOLD,
                                                    color=AppTheme.ON_SURFACE
                                                ),
                                                ft.Text(
                                                    "Configuration & Control",
                                                    size=11,
                                                    color=AppTheme.ON_BACKGROUND,
                                                    opacity=0.7
                                                )
                                            ],
                                            spacing=2,
                                            expand=True
                                        )
                                    ],
                                    spacing=12
                                ),
                                ft.Divider(
                                    height=1,
                                    color=AppTheme.OUTLINE_COLOR
                                )
                            ],
                            spacing=16
                        ),
                        padding=16
                    ),
                    # Navigation items
                    ft.Column(
                        controls=self.nav_items,
                        spacing=4,
                        expand=True
                    )
                ],
                spacing=0,
                expand=True
            ),
            expand=True
        )
