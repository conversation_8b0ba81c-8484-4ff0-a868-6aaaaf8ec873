"""
UUID Generator for Linux Log Collection Agent

This module provides UUID generation functionality for creating unique
identifiers for log entries.
"""

import uuid
import hashlib
import socket
import time
from typing import Optional, Union


class UUIDGenerator:
    """Generates unique identifiers for log entries."""
    
    def __init__(self, namespace: Optional[str] = None):
        """
        Initialize the UUID generator.
        
        Args:
            namespace: Optional namespace for UUID generation
        """
        self.namespace = namespace
        self.hostname = socket.gethostname()
        
        # Create a namespace UUID if provided
        if namespace:
            self.namespace_uuid = uuid.uuid5(uuid.NAMESPACE_DNS, namespace)
        else:
            self.namespace_uuid = None
    
    def generate_uuid4(self) -> str:
        """
        Generate a random UUID4.
        
        Returns:
            UUID4 string
        """
        return str(uuid.uuid4())
    
    def generate_uuid1(self) -> str:
        """
        Generate a time-based UUID1.
        
        Returns:
            UUID1 string
        """
        return str(uuid.uuid1())
    
    def generate_uuid5(self, name: str) -> str:
        """
        Generate a name-based UUID5.
        
        Args:
            name: Name to generate UUID from
            
        Returns:
            UUID5 string
        """
        if self.namespace_uuid:
            return str(uuid.uuid5(self.namespace_uuid, name))
        else:
            return str(uuid.uuid5(uuid.NAMESPACE_DNS, name))
    
    def generate_log_id(self, 
                       log_data: Optional[dict] = None,
                       format_type: str = 'uuid4') -> str:
        """
        Generate a unique log ID.
        
        Args:
            log_data: Optional log data to include in ID generation
            format_type: Type of UUID to generate ('uuid4', 'uuid1', 'uuid5', 'custom')
            
        Returns:
            Unique log ID string
        """
        if format_type == 'uuid4':
            return self.generate_uuid4()
        elif format_type == 'uuid1':
            return self.generate_uuid1()
        elif format_type == 'uuid5':
            if log_data:
                # Create a name from log data
                name_parts = [
                    str(log_data.get('timestamp', '')),
                    str(log_data.get('source', '')),
                    str(log_data.get('message', ''))[:100],  # Limit message length
                    self.hostname
                ]
                name = '|'.join(name_parts)
                return self.generate_uuid5(name)
            else:
                return self.generate_uuid5(f"{time.time()}|{self.hostname}")
        elif format_type == 'custom':
            return self.generate_custom_id(log_data)
        else:
            # Default to UUID4
            return self.generate_uuid4()
    
    def generate_custom_id(self, log_data: Optional[dict] = None) -> str:
        """
        Generate a custom log ID format.
        
        Args:
            log_data: Optional log data to include in ID generation
            
        Returns:
            Custom log ID string
        """
        # Create a custom ID format: timestamp-hostname-hash
        timestamp = int(time.time() * 1000)  # Milliseconds
        hostname_short = self.hostname[:8]  # First 8 chars of hostname
        
        # Create hash from log data or random UUID
        if log_data:
            hash_input = f"{log_data.get('source', '')}{log_data.get('message', '')}"
            hash_value = hashlib.md5(hash_input.encode()).hexdigest()[:8]
        else:
            hash_value = str(uuid.uuid4()).replace('-', '')[:8]
        
        return f"{timestamp}-{hostname_short}-{hash_value}"
    
    def is_valid_uuid(self, uuid_string: str) -> bool:
        """
        Check if a string is a valid UUID.
        
        Args:
            uuid_string: String to validate
            
        Returns:
            True if valid UUID, False otherwise
        """
        try:
            uuid.UUID(uuid_string)
            return True
        except ValueError:
            return False
    
    def extract_timestamp_from_uuid1(self, uuid_string: str) -> Optional[float]:
        """
        Extract timestamp from a UUID1.
        
        Args:
            uuid_string: UUID1 string
            
        Returns:
            Timestamp as float, or None if not a valid UUID1
        """
        try:
            uuid_obj = uuid.UUID(uuid_string)
            if uuid_obj.version == 1:
                # UUID1 timestamp is in 100-nanosecond intervals since 1582-10-15
                timestamp = (uuid_obj.time - 0x01b21dd213814000) / 10000000.0
                return timestamp
            else:
                return None
        except (ValueError, AttributeError):
            return None


# Global UUID generator instance
_uuid_generator = UUIDGenerator()


def generate_log_id(log_data: Optional[dict] = None, 
                   format_type: str = 'uuid4',
                   namespace: Optional[str] = None) -> str:
    """
    Convenience function to generate a log ID.
    
    Args:
        log_data: Optional log data to include in ID generation
        format_type: Type of UUID to generate
        namespace: Optional namespace for UUID generation
        
    Returns:
        Unique log ID string
    """
    if namespace and namespace != _uuid_generator.namespace:
        # Create a new generator with the specified namespace
        generator = UUIDGenerator(namespace)
        return generator.generate_log_id(log_data, format_type)
    else:
        return _uuid_generator.generate_log_id(log_data, format_type)


def is_valid_uuid(uuid_string: str) -> bool:
    """
    Convenience function to check if a string is a valid UUID.
    
    Args:
        uuid_string: String to validate
        
    Returns:
        True if valid UUID, False otherwise
    """
    return _uuid_generator.is_valid_uuid(uuid_string)
