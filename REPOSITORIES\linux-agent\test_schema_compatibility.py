#!/usr/bin/env python3
"""
Test script to verify Linux agent schema compatibility with dashboard.

This script tests that the Linux agent generates logs with the correct field names
that match the updated dashboard schema.
"""

import json
import sys
from datetime import datetime
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

try:
    from logging_agent.agent import LinuxLoggingAgent
    from utils.api_client import LinuxExLogAPIClient
    from config.config_manager import ConfigManager
except ImportError as e:
    print(f"Error importing modules: {e}")
    print("Please ensure you're running this from the linux-agent directory")
    sys.exit(1)


def test_log_schema():
    """Test that generated logs have the correct schema."""
    print("Testing Linux agent log schema compatibility...")
    
    # Create a test log entry
    test_log = {
        'timestamp': datetime.now().strftime('%Y-%m-%dT%H:%M:%S'),
        'source': 'Test',
        'sourceType': 'event',  # Should be sourceType, not source_type
        'host': 'test-host',
        'logLevel': 'info',  # Should be logLevel, not log_level
        'message': 'Test message',
        'additionalFields': {  # Should be additionalFields, not additional_fields
            'test': True
        }
    }
    
    # Test log standardization
    try:
        config_manager = ConfigManager()
        config = config_manager.get_config()
        
        agent = LinuxLoggingAgent()
        standardized_log = agent._standardize_log(test_log)
        
        if not standardized_log:
            print("❌ FAILED: Log standardization returned None")
            return False
        
        # Check required fields with new names
        required_fields = ['logId', 'timestamp', 'source', 'sourceType', 'osType', 'host', 'logLevel', 'message', 'additionalFields']
        
        missing_fields = []
        for field in required_fields:
            if field not in standardized_log:
                missing_fields.append(field)
        
        if missing_fields:
            print(f"❌ FAILED: Missing required fields: {missing_fields}")
            return False
        
        # Check that osType is set to 'linux'
        if standardized_log.get('osType') != 'linux':
            print(f"❌ FAILED: osType should be 'linux', got '{standardized_log.get('osType')}'")
            return False
        
        # Check that old field names are not present
        old_fields = ['log_id', 'source_type', 'log_level', 'additional_fields']
        present_old_fields = []
        for field in old_fields:
            if field in standardized_log:
                present_old_fields.append(field)
        
        if present_old_fields:
            print(f"❌ FAILED: Old field names still present: {present_old_fields}")
            return False
        
        print("✅ PASSED: Log schema is correct")
        print(f"   Generated logId: {standardized_log.get('logId')}")
        print(f"   osType: {standardized_log.get('osType')}")
        print(f"   sourceType: {standardized_log.get('sourceType')}")
        print(f"   logLevel: {standardized_log.get('logLevel')}")
        
        return True
        
    except Exception as e:
        print(f"❌ FAILED: Exception during testing: {e}")
        return False


def test_api_client_validation():
    """Test that API client validates logs correctly."""
    print("\nTesting API client validation...")
    
    try:
        # Create API client with test config
        config = {
            'endpoint': 'http://localhost:5000/api/v1/logs',
            'api_key': 'test-key',
            'validation': {
                'fix_missing_fields': True,
                'default_source': 'System',
                'default_source_type': 'event',
                'default_log_level': 'info'
            }
        }
        
        client = LinuxExLogAPIClient(config)
        
        # Test log with new field names
        test_log = {
            'logId': 'test-123',
            'timestamp': datetime.now().strftime('%Y-%m-%dT%H:%M:%S'),
            'source': 'Test',
            'sourceType': 'event',
            'osType': 'linux',
            'host': 'test-host',
            'logLevel': 'info',
            'message': 'Test message',
            'additionalFields': {'test': True}
        }
        
        validated_log = client._validate_and_fix_log(test_log)
        
        if not validated_log:
            print("❌ FAILED: API client validation returned None")
            return False
        
        # Check that all new field names are preserved
        required_fields = ['logId', 'sourceType', 'osType', 'logLevel', 'additionalFields']
        for field in required_fields:
            if field not in validated_log:
                print(f"❌ FAILED: API client removed required field: {field}")
                return False
        
        print("✅ PASSED: API client validation is correct")
        return True
        
    except Exception as e:
        print(f"❌ FAILED: Exception during API client testing: {e}")
        return False


def main():
    """Run all tests."""
    print("Linux Agent Schema Compatibility Test")
    print("=" * 50)
    
    tests_passed = 0
    total_tests = 2
    
    if test_log_schema():
        tests_passed += 1
    
    if test_api_client_validation():
        tests_passed += 1
    
    print("\n" + "=" * 50)
    print(f"Tests passed: {tests_passed}/{total_tests}")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! Linux agent is compatible with dashboard schema.")
        return 0
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
