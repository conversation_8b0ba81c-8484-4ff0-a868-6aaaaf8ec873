{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to send login notification <NAME_EMAIL>: Email service is not properly configured\u001b[39m","stack":"Error: Email service is not properly configured\n    at EmailService.sendEmail (/app/src/services/emailService.js:93:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async EmailService.sendTemplatedEmail (/app/src/services/emailService.js:335:12)\n    at async EmailService.sendLoginNotificationEmail (/app/src/services/emailService.js:361:12)\n    at async Immediate.<anonymous> (/app/src/routes/auth.js:355:9)","timestamp":"2025-07-16 01:23:11:2311"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to send login notification <NAME_EMAIL>: Email service is not properly configured\u001b[39m","stack":"Error: Email service is not properly configured\n    at EmailService.sendEmail (/app/src/services/emailService.js:93:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async EmailService.sendTemplatedEmail (/app/src/services/emailService.js:335:12)\n    at async EmailService.sendLoginNotificationEmail (/app/src/services/emailService.js:361:12)\n    at async Immediate.<anonymous> (/app/src/routes/auth.js:355:9)","timestamp":"2025-07-16 01:24:27:2427"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to send login notification <NAME_EMAIL>: Email service is not properly configured\u001b[39m","stack":"Error: Email service is not properly configured\n    at EmailService.sendEmail (/app/src/services/emailService.js:93:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async EmailService.sendTemplatedEmail (/app/src/services/emailService.js:335:12)\n    at async EmailService.sendLoginNotificationEmail (/app/src/services/emailService.js:361:12)\n    at async Immediate.<anonymous> (/app/src/routes/auth.js:355:9)","timestamp":"2025-07-16 01:28:38:2838"}
