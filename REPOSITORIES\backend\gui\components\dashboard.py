"""
Dashboard component showing system overview and status
"""

import flet as ft
import asyncio
import psutil
from datetime import datetime
from typing import Optional
import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from gui.utils.theme import AppTheme
from service.windows_service import get_service_status

class Dashboard(ft.Control):
    """Dashboard component"""
    
    def __init__(self, page: ft.Page, logger):
        super().__init__()
        self.page = page
        self.logger = logger
        
        # Status indicators
        self.service_status_text: Optional[ft.Text] = None
        self.service_status_icon: Optional[ft.Icon] = None
        self.cpu_usage_text: Optional[ft.Text] = None
        self.memory_usage_text: Optional[ft.Text] = None
        self.disk_usage_text: Optional[ft.Text] = None
        self.uptime_text: Optional[ft.Text] = None
        
        # Auto-refresh
        self.auto_refresh = True
        self.refresh_interval = 5  # seconds
        
    async def initialize(self):
        """Initialize the dashboard"""
        try:
            self.logger.info("Initializing dashboard")
            
            # Create status indicators
            self._create_status_indicators()
            
            # Start auto-refresh
            asyncio.create_task(self._auto_refresh_loop())
            
            self.logger.info("Dashboard initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Error initializing dashboard: {e}")
            raise
    
    def _create_status_indicators(self):
        """Create status indicator controls"""
        self.service_status_text = ft.Text("Unknown", size=14, weight=ft.FontWeight.W_500)
        self.service_status_icon = ft.Icon(ft.icons.HELP, size=20)
        self.cpu_usage_text = ft.Text("0%", size=14, weight=ft.FontWeight.W_500)
        self.memory_usage_text = ft.Text("0%", size=14, weight=ft.FontWeight.W_500)
        self.disk_usage_text = ft.Text("0%", size=14, weight=ft.FontWeight.W_500)
        self.uptime_text = ft.Text("Unknown", size=14, weight=ft.FontWeight.W_500)
    
    async def _auto_refresh_loop(self):
        """Auto-refresh loop for real-time updates"""
        while self.auto_refresh:
            try:
                await self.refresh()
                await asyncio.sleep(self.refresh_interval)
            except Exception as e:
                self.logger.error(f"Error in auto-refresh loop: {e}")
                await asyncio.sleep(self.refresh_interval)
    
    async def refresh(self):
        """Refresh dashboard data"""
        try:
            # Update service status
            await self._update_service_status()
            
            # Update system metrics
            await self._update_system_metrics()
            
            # Update UI
            await self.update_async()
            
        except Exception as e:
            self.logger.error(f"Error refreshing dashboard: {e}")
    
    async def _update_service_status(self):
        """Update service status"""
        try:
            status = get_service_status()
            
            if status:
                if "Running" in status:
                    self.service_status_text.value = "Running"
                    self.service_status_text.color = AppTheme.SUCCESS_COLOR
                    self.service_status_icon.name = ft.icons.CHECK_CIRCLE
                    self.service_status_icon.color = AppTheme.SUCCESS_COLOR
                elif "Stopped" in status:
                    self.service_status_text.value = "Stopped"
                    self.service_status_text.color = AppTheme.ERROR_COLOR
                    self.service_status_icon.name = ft.icons.STOP_CIRCLE
                    self.service_status_icon.color = AppTheme.ERROR_COLOR
                else:
                    self.service_status_text.value = status
                    self.service_status_text.color = AppTheme.WARNING_COLOR
                    self.service_status_icon.name = ft.icons.WARNING
                    self.service_status_icon.color = AppTheme.WARNING_COLOR
            else:
                self.service_status_text.value = "Unknown"
                self.service_status_text.color = AppTheme.ON_BACKGROUND
                self.service_status_icon.name = ft.icons.HELP
                self.service_status_icon.color = AppTheme.ON_BACKGROUND
                
        except Exception as e:
            self.logger.error(f"Error updating service status: {e}")
            self.service_status_text.value = "Error"
            self.service_status_text.color = AppTheme.ERROR_COLOR
            self.service_status_icon.name = ft.icons.ERROR
            self.service_status_icon.color = AppTheme.ERROR_COLOR
    
    async def _update_system_metrics(self):
        """Update system performance metrics"""
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=0.1)
            self.cpu_usage_text.value = f"{cpu_percent:.1f}%"
            self.cpu_usage_text.color = self._get_usage_color(cpu_percent)
            
            # Memory usage
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            self.memory_usage_text.value = f"{memory_percent:.1f}%"
            self.memory_usage_text.color = self._get_usage_color(memory_percent)
            
            # Disk usage
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            self.disk_usage_text.value = f"{disk_percent:.1f}%"
            self.disk_usage_text.color = self._get_usage_color(disk_percent)
            
            # System uptime
            boot_time = datetime.fromtimestamp(psutil.boot_time())
            uptime = datetime.now() - boot_time
            days = uptime.days
            hours, remainder = divmod(uptime.seconds, 3600)
            minutes, _ = divmod(remainder, 60)
            
            if days > 0:
                self.uptime_text.value = f"{days}d {hours}h {minutes}m"
            else:
                self.uptime_text.value = f"{hours}h {minutes}m"
            
        except Exception as e:
            self.logger.error(f"Error updating system metrics: {e}")
    
    def _get_usage_color(self, percentage: float) -> str:
        """Get color based on usage percentage"""
        if percentage < 50:
            return AppTheme.SUCCESS_COLOR
        elif percentage < 80:
            return AppTheme.WARNING_COLOR
        else:
            return AppTheme.ERROR_COLOR
    
    def _create_status_card(self, title: str, icon: ft.Icon, value_control: ft.Control, description: str = "") -> ft.Container:
        """Create a status card"""
        return ft.Container(
            content=ft.Column(
                controls=[
                    ft.Row(
                        controls=[
                            icon,
                            ft.Text(title, size=14, weight=ft.FontWeight.W_500, color=AppTheme.ON_SURFACE)
                        ],
                        spacing=8
                    ),
                    value_control,
                    ft.Text(description, size=11, color=AppTheme.ON_BACKGROUND, opacity=0.7) if description else ft.Container()
                ],
                spacing=8,
                tight=True
            ),
            **AppTheme.get_card_style(),
            expand=True
        )
    
    def build(self):
        """Build the dashboard"""
        return ft.Container(
            content=ft.Column(
                controls=[
                    # Header
                    ft.Container(
                        content=ft.Row(
                            controls=[
                                ft.Text(
                                    "Dashboard",
                                    size=24,
                                    weight=ft.FontWeight.BOLD,
                                    color=AppTheme.ON_SURFACE
                                ),
                                ft.Text(
                                    f"Last updated: {datetime.now().strftime('%H:%M:%S')}",
                                    size=12,
                                    color=AppTheme.ON_BACKGROUND,
                                    opacity=0.7
                                )
                            ],
                            alignment=ft.MainAxisAlignment.SPACE_BETWEEN
                        ),
                        padding=20
                    ),
                    
                    # Status cards
                    ft.Container(
                        content=ft.Column(
                            controls=[
                                # Service status
                                ft.Row(
                                    controls=[
                                        self._create_status_card(
                                            "Service Status",
                                            self.service_status_icon,
                                            self.service_status_text,
                                            "Windows service state"
                                        )
                                    ]
                                ),
                                
                                # System metrics
                                ft.Row(
                                    controls=[
                                        self._create_status_card(
                                            "CPU Usage",
                                            ft.Icon(ft.icons.MEMORY, size=20, color=AppTheme.INFO_COLOR),
                                            self.cpu_usage_text,
                                            "Current CPU utilization"
                                        ),
                                        self._create_status_card(
                                            "Memory Usage",
                                            ft.Icon(ft.icons.STORAGE, size=20, color=AppTheme.INFO_COLOR),
                                            self.memory_usage_text,
                                            "RAM utilization"
                                        ),
                                        self._create_status_card(
                                            "Disk Usage",
                                            ft.Icon(ft.icons.HARD_DRIVE, size=20, color=AppTheme.INFO_COLOR),
                                            self.disk_usage_text,
                                            "Primary disk utilization"
                                        )
                                    ],
                                    spacing=16
                                ),
                                
                                # Additional info
                                ft.Row(
                                    controls=[
                                        self._create_status_card(
                                            "System Uptime",
                                            ft.Icon(ft.icons.SCHEDULE, size=20, color=AppTheme.INFO_COLOR),
                                            self.uptime_text,
                                            "Time since last boot"
                                        )
                                    ]
                                )
                            ],
                            spacing=16
                        ),
                        padding=20,
                        expand=True
                    )
                ],
                spacing=0,
                expand=True
            ),
            expand=True,
            bgcolor=AppTheme.BACKGROUND_COLOR
        )
